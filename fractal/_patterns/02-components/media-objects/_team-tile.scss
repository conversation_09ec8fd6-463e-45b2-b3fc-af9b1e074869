
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.team-tile {
    --block-padding: #{$spacing};
    --inline-padding: #{$spacing*1.5};

    @include screen($bp360) {
        --block-padding: #{$spacing*2};
        --inline-padding: #{$spacing*2.5};
    }

    text-decoration: none;
    color: inherit;
    background-color: $color-white;
    display: grid;
    grid-auto-rows: min-content auto;
    gap: 0;

    &[href]:hover,
    &[href]:focus,
    &[href]:active,
    &[role="link"]:hover,
    &[role="link"]:focus,
    &[role="link"]:active {
        box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.12);
        outline: $color-grey-02 solid 2px;
    }


    &__textarea {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        order: 1;
        padding: calc(var(--block-padding) * 1.5) var(--inline-padding) var(--block-padding);
        border: 1px solid $color-grey-02;
        border-top-width: 0;
    }

    &__title {
        font-size: var(--h5-font-size);
        margin-bottom: .25em;
    }

    &__position {
        margin: 0;
        text-wrap: balance;
        line-height: 1.25;
    }

    &__img {
        aspect-ratio: 16 / 9;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        svg {
            width: 100% !important;
            height: 100% !important;
            display: block;
            min-width: 100%;
            min-height: 100%;
        }
    }

}


// Team list styles

.team-members {
    display: grid;
    gap: var(--tight-gap);
    grid-template-columns: repeat(auto-fit, minmax(20ch, 1fr));

    .team-tile {
        max-width: 40ch;
    }
}


.team-contact-details {
    margin-bottom: $spacing*3;
    display: grid;
    grid-auto-flow: column;
    gap: var(--tight-gap);

    :where(&) svg {
        size: 1em;
        height: 1em;
        @include set-svg-colour($color-primary);
        margin: auto;
        align-self: center;
        justify-self: center;
    }

    &__item {
        display: grid;
        grid-template-columns: 1.5em auto;
        gap: $spacing;
        justify-content: start;
        align-items: center;

        &__title {
            @include vis-hidden;
        }
    }
}

// TODO: This should have it's own partial but I'm strapped for time
.filter-bar {
    display: grid;
    grid-template-columns: max-content auto auto;
    gap: $spacing*2;
    align-items: center;
    margin-bottom: var(--tight-gap);

}

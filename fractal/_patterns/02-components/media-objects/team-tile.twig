<!-- Template: {{ _self }}.twig -->

{#
    Data setup
#}

{% if fractal is not defined %}
    {% set profilePhoto = entry.profilePhoto.one() %}

    {% if profilePhoto %}
        {% set profilePhotoTransforms =
            profilePhoto.getImg({ width: 1088, height: 612 }, ['1.5x', '2x', '3x'])|attr({
                loading: 'lazy',
                alt: 'Photo of ' ~ entry.title,
            })
        %}
    {% endif %}

{% else %}
    {# Doesn't support the different srcset resolutions (yet) #}
    {% set profilePhotoTransforms = "<img src=\"" ~ profilePhoto ~ "\">" %}
{% endif %}

{#
    Tile structure
#}

{% tag profileLink ? 'a' : 'div' with {
    class: 'team-tile ' ~ modifierClass ?? false,
    href: entry.url ?? null
} %}

    {% if profilePhoto %}
    <div class="team-tile__img">
        {{ profilePhotoTransforms|raw }}
    </div>
    {% else %}
    <div class="team-tile__img team-tile__img--fallback">

        <svg viewBox="0 0 600 600" preserveAspectRatio="none" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <rect id="Rectangle" fill="#0B1F41" x="0" y="0" width="600" height="600"></rect>
            </g>
        </svg>

    </div>
    {% endif %}

    <div class="team-tile__textarea">

        <h3 class="team-tile__title">{{ entry.title }}</h3>

        {% if entry.staffPositionTitle ?? false %}
        <p class="team-tile__position">{{ entry.staffPositionTitle.one() }}</p>
        {% endif %}

    </div>

{% endtag %}


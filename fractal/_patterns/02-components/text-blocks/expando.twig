<!-- Template: {{ _self }} -->

<div class="expando-item">

{% if item.title ?? false %}
    <h3>{{ item.title }}</h3>
{% endif %}

{% if item.richtext|length %}
    {{ item.richtext }}
{% endif %}

{% if not (item.title ?? false and item.richtext|length) %}
    <p></p> {# Hacky fix for JS bug when no content before expand.  #}
{% endif %}

    <div class="expando">
        {{ item.expandoContent }}
    </div>

</div>

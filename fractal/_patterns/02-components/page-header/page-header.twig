<!-- Template: {{ _self }}.twig -->

{% if pageSubtitle is not defined and entry is defined and entry.subtitle ?? false %}
    {% set pageSubtitle = entry.subtitle %}
{% endif %}

{% if showBreadcrumb ?? false %}
    {% set showBreadcrumb = showBreadcrumb %}
{% elseif entry is defined and entry.type('innerPage') and entry.level == 1 %}
    {% set showBreadcrumb = false %}
{% else %}
    {% set showBreadcrumb = true %}
{% endif %}


<hgroup class="page-header">
    <h1 class="page-header__heading">

        <span class="page-header__title">
            {{ entry.bannerTitle ?? false ? entry.bannerTitle : pageTitle }}
        </span>

        {% if pageSubtitle ?? false %}
            <span class="page-header__subtitle">
                {{ pageSubtitle }}
            </span>
        {% endif %}
    </h1>
    {% if showBreadcrumb ?? false %}
        {% include "01_core/_layouts/breadcrumb" %}
    {% endif %}
</hgroup>

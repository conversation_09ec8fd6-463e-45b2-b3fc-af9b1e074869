<?php

use craft\helpers\App;
use yii\redis\Session;

return [
    'components' => [
        'session' => function() {
            $config = App::sessionConfig();

            $env = CRAFT_ENVIRONMENT;
            $hostname = getenv('SITES_REDIS_HOSTNAME') ?: 'localhost';

            if ($env === 'production' || $env == 'staging') {
                $config['class'] = Session::class;
                $config['keyPrefix'] = "pht2:session:{$env}:";
                $config['redis'] = [
                    'hostname' => $hostname,
                    'port' => 6379,
                ];
            }

            return Craft::createObject($config);
        }
    ],
];
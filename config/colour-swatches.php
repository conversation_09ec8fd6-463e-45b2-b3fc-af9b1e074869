<?php
/**
 * color-swatches plugin for Craft CMS 3.x.
 *
 * Let clients choose from a predefined set of colours.
 *
 * @link      https://percipio.london
 *
 * @copyright Copyright (c) 2020 Percipio.London
 */

/**
 * colour-swatches config.php.
 *
 * This file exists only as a template for the color-swatches settings.
 * It does nothing on its own.
 *
 * Don't edit this file, instead copy it to 'craft/config' as 'colour-swatches.php'
 * and make your changes there to override default settings.
 *
 * Once copied to 'craft/config', this file will be multi-environment aware as
 * well, so you can have different settings groups for each environment, just as
 * you do for 'general.php'
 */

return [

    // Custom  palettes, fixed options [label, default (boolean), colour (array(colour, customOptions)) ]
    'palettes' => [
    //     'Feature Colour Options' => [  // custom label
    //         [
    //             'label'   => '#001F5b',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#001F5b'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#008FC6',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#008FC6'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#00A7B6',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#00A7B6'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#00AEEF',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#00AEEF'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#00B8BC',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#00B8BC'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#013E7F',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#013E7F'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#01A371',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#01A371'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    //         ],
    //         [
    //             'label'   => '#0B7454',
    //             'default' => false,
    //             'color' => [
    //                 [
    //                     'color' => '#0B7454'               // the colour shown in the fieldtype (required)
    //                 ]
    //             ]
    // ],
    //     ]
    ]

];

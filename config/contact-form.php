<?php
/*
    this file is only necessary if you're
    using the Pixel & Tonic contact-form plugin
    AND
    You've split the message object to include additional fields

*/
use craft\contactform\models\Submission;
use yii\base\Event;

Event::on(Submission::class, Submission::EVENT_AFTER_VALIDATE, function(Event $e) {
    /** @var Submission $submission */
    $submission = $e->sender;

    // Make sure that `message[Phone]` was filled in
    if (empty($submission->message['Phone'])) {
        // Add the error
        // (This will be accessible via `message.getErrors('message.phone')` in the template.)
        $submission->addError('message.phone', 'A phone number is required.');
    }

     // Make sure that `message[Address]` was filled in
     if (empty($submission->message['Address'])) {
        // Add the error
        // (This will be accessible via `message.getErrors('message.phone')` in the template.)
        $submission->addError('message.address', 'An address is required.');
    }
});

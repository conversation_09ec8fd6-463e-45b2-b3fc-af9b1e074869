<?php
/**
 * Yii Application Config
 *
 * Edit this file at your own risk!
 *
 * The array returned by this file will get merged with
 * vendor/craftcms/cms/src/config/app.php and app.[web|console].php, when
 * Craft's bootstrap script is defining the configuration for the entire
 * application.
 *
 * You can define custom modules and system components, and even override the
 * built-in system components.
 *
 * If you want to modify the application config for *only* web requests or
 * *only* console requests, create an app.web.php or app.console.php file in
 * your config/ folder, alongside this one.
 */

use yii\redis\Cache;

Craft::setAlias('@modules', realpath(__DIR__ . '/../modules'));

return [
    'modules' => [
        'module' => \modules\Module::class,
        'content-panel' => \modules\contentpanel\ContentPanelModule::class,
        'ckeditor-customisations' => \modules\ckeditorcustomisations\src\CkeditorCustomisations::class,
        'mintpayment' => \modules\mintpayment\MintPaymentModule::class,
        'registration-reporting' => \modules\registrationreporting\RegistrationReportingModule::class,
        'payment-reporting' => \modules\paymentreporting\PaymentReportingModule::class,
        'submission-reporting' => \modules\submissionreporting\SubmissionReporting::class,
        'attendees-limit' => \modules\attendeeslimit\AttendeesLimit::class,
        'attendees-reporting' => \modules\attendeesreporting\AttendeesReporting::class,
        'url-upload-assets' => \modules\urluploadassets\UrlUploadAssets::class,
    ],
    'bootstrap' => [
        'module',
        'content-panel',
        'ckeditor-customisations',
        'mintpayment',
        'registration-reporting',
        'payment-reporting',
        'submission-reporting',
        'attendees-limit',
        'attendees-reporting',
        'url-upload-assets',
    ],
    'components' => [
        'cache' => function() {
            $env = CRAFT_ENVIRONMENT;
            $hostname = getenv('SITES_REDIS_HOSTNAME') ?: 'localhost';

            $config = [
                'class' => Cache::class,
                'keyPrefix' => "pht2:cache:{$env}:",
                'defaultDuration' => Craft::$app->config->general->cacheDuration,
                'shareDatabase' => true,

                'redis' => [
                    'hostname' => $hostname,
                    'port' => 6379,
                ],
            ];

            return Craft::createObject($config);
        },
    ],
];

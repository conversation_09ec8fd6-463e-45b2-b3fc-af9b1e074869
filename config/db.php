<?php
		/**
		 * Database Configuration
		 *
		 * All of your system's database connection settings go in here. You can see a
		 * list of the available settings in vendor/craftcms/cms/src/config/DbConfig.php.
		 *
		 * @see craft\config\DbConfig
		 *
		 * NOTE: This has been modified to suit Karmanbunny's local environment variables
		 * GetEnv in this instance references server configuration (apache/fpm conf)
		 * We are not using the Craft .env file, or the PHP DotEnv loader
		 */

		return [
		   'driver' => 'mysql',
		   'server' => getenv('SITES_DB_HOSTNAME'),
		   'user' => getenv('SITES_DB_USERNAME'),
		   'password' => getenv('SITES_DB_PASSWORD'),
		   'database' => getenv('SITES_DB_DATABASE'),
		   'tablePrefix' => 'kb_',

		   // 'schema' => getenv('DB_SCHEMA'),
		   // 'port' => getenv('DB_PORT')
		];

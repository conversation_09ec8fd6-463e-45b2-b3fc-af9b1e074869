dateModified: 1748568836
elementSources:
  craft\elements\Asset:
    -
      defaultSort:
        - dateCreated
        - desc
      disabled: false
      key: 'volume:623db196-498d-4c4b-ab91-8d16301180f3' # Images
      tableAttributes:
        - filename
        - size
        - dateModified
        - uploader
        - link
        - 'fieldInstance:4cf0df05-26db-4017-b312-2d057d1f7413'
      type: native
    -
      key: 'volume:8b18b1ba-b9ba-4308-b4f8-bec8c72b9a42' # Documents
      type: native
    -
      key: 'volume:88da6c74-0b14-4006-8ce2-77db52c368e9' # Videos
      type: native
    -
      key: 'volume:89e7e7df-e72c-4b4d-9d61-1d8d8ac63698' # Icons
      type: native
    -
      key: temp
      type: native
  craft\elements\Entry:
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: '*'
      tableAttributes:
        - section
        - postDate
        - expiryDate
        - link
      type: native
    -
      defaultSort:
        - title
        - asc
      defaultViewMode: ''
      disabled: false
      key: singles
      tableAttributes:
        - link
        - section
        - type
      type: native
    -
      heading: Pages
      type: heading
    -
      defaultSort:
        - structure
        - asc
      disabled: false
      key: 'section:a9fa4bce-4bdd-4816-bd77-97a96602fd81' # Home
      tableAttributes:
        - expiryDate
        - type
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:8e022768-bf63-43c1-ad96-2f193cd7f337' # Pages
      tableAttributes:
        - postDate
        - expiryDate
        - link
        - type
      type: native
    -
      heading: Collections
      type: heading
    -
      defaultSort:
        - dateUpdated
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:5731f108-a53e-4151-8bd9-90209d1c39f0' # Products
      tableAttributes:
        - 'field:a6715600-a3f0-4267-beb5-4a645b0a2e24' # Price
        - 'field:cdc958b8-d44e-46af-b762-566867461436' # Supplier/s
        - status
        - expiryDate
        - link
        - 'field:b470d9c4-c913-4b59-9916-9122703a1b98' # Online booking deposit
        - dateUpdated
      type: native
    -
      defaultSort:
        - expiryDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:4f628dba-ffe2-4716-9651-474278dbbfbe' # Airfares
      tableAttributes:
        - 'field:a6715600-a3f0-4267-beb5-4a645b0a2e24' # Price
        - 'field:e3fc8a4c-fd5c-4594-b702-beb33e41bda3' # Airfare Class
        - 'field:b58d0585-f0af-4b67-9172-d3ea303a75c4' # Destinations
        - 'fieldInstance:85c957d0-759a-4d2e-ab52-15bf76cc3bd2'
        - expiryDate
        - link
        - 'field:372d10b9-60bc-4454-bc4d-ff6512dafc1c' # Airlines
        - status
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:5b37ab64-dc24-4a5e-b54e-7d4452e0e96b' # Blog
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - dateUpdated
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:bf55692e-49e4-4124-9250-18ec8824aafb' # Newsletters
      tableAttributes:
        - status
        - postDate
        - link
        - type
      type: native
    -
      defaultSort:
        - dateUpdated
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:fcb6fd6a-e9fd-4794-a829-8a2e7cf8993c' # Team
      tableAttributes:
        - 'field:c21ca1df-6339-4dc6-9836-8c5e51908f8c' # Profile photo
        - 'field:a95cd64c-7043-4819-94c5-81ce8a5b272a' # Departments
        - 'field:52b92093-bda6-49a8-a1e5-e859db6fa6de' # Staff Position Title
        - link
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:bec30a08-3b3c-4d05-80ae-fb17f8f4344f' # Destinations
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:39c3595c-44e4-4c20-9957-ffd6e104c6d9' # Suppliers
      tableAttributes:
        - status
        - link
        - type
      type: native
    -
      defaultSort:
        - section
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:ad2295ee-2dd4-4c3a-9478-3d2d4602c42a' # Branches
      tableAttributes:
        - status
        - expiryDate
        - link
        - 'field:1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a' # Image
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:0d0d395b-bb16-464a-a3bd-5d8ad4950eda' # Testimonials
      tableAttributes:
        - 'field:692981e9-ded2-4931-bee6-40070fe65c66' # Subtitle
        - status
        - postDate
        - expiryDate
        - authors
        - link
        - 'field:ee46dd7f-8e89-496f-94c4-8fe22ad22bc5' # Client Type
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:dea9fa44-3419-47e4-9824-82b405e078b6' # Brochures
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
        - 'field:1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a' # Image
      type: native
    -
      heading: 'Event Collections'
      type: heading
    -
      defaultSort:
        - dateUpdated
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:8be355f4-8e66-4141-8056-4ae6b82d269c' # Events
      tableAttributes:
        - status
        - authors
        - link
        - type
        - 'field:8a8888af-000e-4451-a668-0b2fcca66005' # Date
        - expiryDate
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:6e2195b7-55a8-429f-bcc4-73c98efa3f75' # Expo Sessions
      tableAttributes:
        - status
        - expiryDate
        - authors
        - link
        - 'field:833fefac-ec51-4218-9aef-081f37a380ad' # Cinema Location
        - 'field:6f255c3b-0320-4611-a933-0be2b9c7f380' # Item Title
        - 'field:4248e721-e035-4bf5-9f20-c2e65c57720a' # Time
      type: native
    -
      heading: Partials
      type: heading
    -
      defaultSort:
        - dateUpdated
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:ebc45aee-24a8-4c5d-8435-8d961d154c97' # Page Add-ons
      tableAttributes:
        - postDate
        - expiryDate
        - link
        - type
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:81d31ef9-650d-4515-8378-07e1aaa3920e' # Alerts
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
        - type
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:edd80c85-d160-4007-9f0e-284c5f68f27f' # Primary Navigation
      tableAttributes:
        - postDate
        - expiryDate
        - link
        - type
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:f76d5813-c986-4e10-bf0d-ec4a9537493b' # Footer
      tableAttributes:
        - postDate
        - expiryDate
        - link
        - type
      type: native
    -
      heading: Categories
      type: heading
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:c345d6df-715a-475d-b0b3-f48a8d61099d' # Blog Categories
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
        - type
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:e614730f-0e9d-475d-be9d-98131c01126c' # Product Categories
      tableAttributes:
        - type
        - status
        - expiryDate
        - 'field:7d9a07fe-084f-4520-84c8-f73991962aac' # Icon
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:85cfe2cd-e627-4802-b4f5-3964ffc8f425' # PHT Departments & Positions
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - type
      type: native
    -
      heading: ''
      type: heading
    -
      heading: ''
      type: heading
email:
  fromEmail: <EMAIL>
  fromName: 'Phil Hoffmann Travel'
  replyToEmail: null
  template: 01_core/_emails/email.twig
  transportSettings:
    host: smtp.postmarkapp.com
    password: ************************************
    port: '587'
    useAuthentication: '1'
    username: ************************************
  transportType: craft\mail\transportadapters\Smtp
fs:
  contentDocuments:
    hasUrls: true
    name: Documents
    settings:
      addSubfolderToRootUrl: true
      autoFocalPoint: false
      bucket: $S3_BUCKET_NAME
      bucketSelectionMode: manual
      cfDistributionId: ''
      cfPrefix: ''
      expires: ''
      keyId: $AWS_ACCESS_KEY
      makeUploadsPublic: false
      region: $AWS_REGION
      secret: $AWS_SECRET_KEY
      storageClass: ''
      subfolder: documents
    type: craft\awss3\Fs
    url: $S3_PUBLIC_URL
  contentIcons:
    hasUrls: true
    name: Icons
    settings:
      addSubfolderToRootUrl: true
      autoFocalPoint: false
      bucket: $S3_BUCKET_NAME
      bucketSelectionMode: manual
      cfDistributionId: ''
      cfPrefix: ''
      expires: ''
      keyId: $AWS_ACCESS_KEY
      makeUploadsPublic: false
      region: $AWS_REGION
      secret: $AWS_SECRET_KEY
      storageClass: ''
      subfolder: icons
    type: craft\awss3\Fs
    url: $S3_PUBLIC_URL
  contentImages:
    hasUrls: true
    name: Images
    settings:
      addSubfolderToRootUrl: true
      autoFocalPoint: false
      bucket: $S3_BUCKET_NAME
      bucketSelectionMode: manual
      cfDistributionId: ''
      cfPrefix: ''
      expires: ''
      keyId: $AWS_ACCESS_KEY
      makeUploadsPublic: false
      region: $AWS_REGION
      secret: $AWS_SECRET_KEY
      storageClass: ''
      subfolder: images
    type: craft\awss3\Fs
    url: $S3_PUBLIC_URL
  contentVideos:
    hasUrls: true
    name: Videos
    settings:
      addSubfolderToRootUrl: true
      autoFocalPoint: false
      bucket: $S3_BUCKET_NAME
      bucketSelectionMode: manual
      cfDistributionId: ''
      cfPrefix: ''
      expires: ''
      keyId: $AWS_ACCESS_KEY
      makeUploadsPublic: false
      region: $AWS_REGION
      secret: $AWS_SECRET_KEY
      storageClass: ''
      subfolder: videos
    type: craft\awss3\Fs
    url: $S3_PUBLIC_URL
  generatedPdfs:
    hasUrls: true
    name: 'Generated Content'
    settings:
      addSubfolderToRootUrl: true
      autoFocalPoint: false
      bucket: $S3_BUCKET_NAME
      bucketSelectionMode: manual
      cfDistributionId: ''
      cfPrefix: ''
      expires: ''
      keyId: $AWS_ACCESS_KEY
      makeUploadsPublic: false
      region: $AWS_REGION
      secret: $AWS_SECRET_KEY
      storageClass: ''
      subfolder: pdfs
    type: craft\awss3\Fs
    url: $S3_PUBLIC_URL
meta:
  __names__:
    0a1d327b-a849-4fbf-93d5-bb60a2d3e2d3: 'Blog Posts Gallery' # Blog Posts Gallery
    0ac5c84e-bae0-4653-a97a-3aace2b68426: 'Download link type' # Download link type
    0adc5399-0e40-4d46-9443-a85046d2751d: 'Page Content' # Page Content
    0ba3d635-4757-49b3-a1a2-090080849089: Mint # Mint
    0bb12b29-8fbd-49ee-968e-d6c58cbd0c60: Duration # Duration
    0c50a9a4-5506-48d9-b974-2ed9cbf6fb96: Products # Products
    0cd3b86e-1343-4a02-848f-0f38d7a6552a: 'Google Map Embed' # Google Map Embed
    0d0d395b-bb16-464a-a3bd-5d8ad4950eda: Testimonials # Testimonials
    0d8e1935-bad5-4bdd-9db5-beae28fb933e: 'Device Visibility' # Device Visibility
    0d420372-a13e-4813-99fc-5fddc267fb34: Limit # Limit
    0da199ad-61c2-4af2-b290-0f966bfc0931: '3 Letter Location Code' # 3 Letter Location Code
    0e24bde5-fe56-4c6e-9968-130ebbb116f5: 'URL does not contain' # URL does not contain
    0f7d4432-d3a8-401f-82f5-95e05e179deb: Transform # Transform
    1a1513ef-a199-49b4-b879-945e308fb54a: 'Metadata Share Image' # Metadata Share Image
    1b30eb25-6fa1-45f1-b501-20cca5ac9e73: 'Promo Card Helper' # Promo Card Helper
    1bc02563-f940-42c1-b38f-6358d4cb3e3d: 'Target Entry' # Target Entry
    1c0616dc-ee99-4743-8b19-df2186df0da4: 'Third Party Digital Agencies' # Third Party Digital Agencies
    1c7ae795-dd84-4c72-a4b9-4ca2eaf074c8: 'URL contains' # URL contains
    1d5fbdfe-60b1-424a-81d2-ab575a06e823: 'Enable Popup' # Enable Popup
    1e4d794d-47b7-44b1-b466-54bbb1600c54: 'Page Entry' # Page Entry
    1f06125f-ff3e-4487-9785-2cb5235b95fe: 'Submission Reporting Enabled' # Submission Reporting Enabled
    1f271e29-cf3d-46a5-9ad9-c04ccf0fd94a: Image # Image
    2b31ecb8-c987-4347-944b-100d09b2e876: 'Default Social Media image' # Default Social Media image
    2b124fae-a311-4906-ba02-e8bc4579cb77: 'Column Gap' # Column Gap
    2cd2250e-9c06-49e4-b78d-e1b0713ddd44: Formie # Formie
    2d22cfd6-5e48-4b8e-b6f2-5ad2c1cc0157: 'Image List Grid Columns' # Image List Grid Columns
    2dfae8d6-27ac-4ced-a642-9b432dde0a6b: Airfare # Airfare
    2e643870-3ad2-4de3-9263-313f4336543f: 'Postal Address' # Postal Address
    2eb5827d-16d0-4c6c-9259-505bea915c1e: 'Phil Hoffmann Travel' # Phil Hoffmann Travel
    2ef4053c-b29f-46df-9059-c80185d7952c: 'Primary Site' # Primary Site
    2f4c6551-4431-4352-8c81-6989358d2bbc: 'Image List' # Image List
    2f4881f2-619d-484d-8bae-035941bc9724: 'Navigation Visibility' # Navigation Visibility
    2ff3e54d-2743-4d85-8877-84c8cfa85792: 'Image Alt Text' # Image Alt Text
    3a39688e-4c66-4dde-af4a-1523c1747578: 'Submission Reporting Email Address' # Submission Reporting Email Address
    3f2f52cf-acfc-434f-a41a-aef756133fee: 'Expiry Date' # Expiry Date
    3fb9a3c8-4748-4d5b-af33-e64794219ce7: 'Submission Reports' # Submission Reports
    4a093217-dfb7-4f50-8c3d-14596c29e9bd: 'Vertical Gap' # Vertical Gap
    4b044761-5d09-4c4a-9d7d-8e0d1082c639: 'Facebook URL' # Facebook URL
    4b2d2e78-c26e-4b2a-87a4-b17fd201637f: Align # Align
    4b5454e0-cbca-4f80-872b-a94f846683db: Image # Image
    4bf42a20-2d67-4da7-8d44-44cf1f8d234d: 'Percentage Deposit Price' # Percentage Deposit Price
    4c30d470-bee6-4eec-bf41-a059ee3dde99: Suppliers # Suppliers
    4c88e466-6cc9-4de7-901a-ed0fd72cc3f7: 'Group Event Registration' # Group Event Registration
    4c2988ee-1839-48ba-a76a-656b50c65ba4: 'Departure Destination' # Departure Destination
    04c9645d-ce63-4f71-930f-f0438bd168f3: Banner # Banner
    4d62c876-d02d-486f-8f5f-5f45aa416d97: 'TikTok URL' # TikTok URL
    4d427b02-5983-45df-bb51-21d51d2681a4: 'Enquiry Form' # Enquiry Form
    4dae4586-b8d5-46b1-8e83-a1d4f1bb9f94: 'Deposit Type' # Deposit Type
    4e9bd440-6cb5-4a54-aeb7-f09310154c78: 'Text Content' # Text Content
    4ea90ed5-6407-4264-8861-04a113d93c3a: PDF # PDF
    4ed4074e-eb71-49d9-b9a9-05f308dcd29a: 'Navigation Title' # Navigation Title
    04f12f15-4e1c-4011-a7b5-7dd0321d0cbc: 'Team Members' # Team Members
    4f628dba-ffe2-4716-9651-474278dbbfbe: Airfares # Airfares
    4fb9156c-4e2c-421e-9f0d-5c5e220035d4: Layout # Layout
    5ac0236d-025c-469b-9e6c-8c2a061656ad: 'Extra content close button' # Extra content close button
    5b8b6130-5a6b-442e-93b6-16dfb95fe608: Search # Search
    5b34bb6b-a7ee-41ca-998b-9b584fa9f31b: 'Mint Payments API Key' # Mint Payments API Key
    5b37ab64-dc24-4a5e-b54e-7d4452e0e96b: Blog # Blog
    5d9d7c32-46c7-4584-bc69-b78fcc58dbf4: Supplier # Supplier
    5d788ca3-d304-45ce-81e7-3c6e7e30b01a: 'Fallback Behaviour' # Fallback Behaviour
    5db558bc-3b1b-490f-bed2-5580e0996c12: 'Navigation Link Helper' # Navigation Link Helper
    5e09a4ea-6c25-41ac-ba2a-82ca4a66fbce: 'Overview Section Title' # Overview Section Title
    5e71292b-2fc0-4221-9fcb-a56c94453384: Meta # Meta
    05ea72d0-1cc0-4343-bb3b-049be6b22dab: 'Full Profile Page Links' # Full Profile Page Links
    5f4f1d2c-a3ac-4d35-a0ae-e3cd42b7e686: 'Dates/Pricing Section title' # Dates/Pricing Section title
    6a8ad9ce-1542-4597-9d27-2b6ed1f98469: 'External URL' # External URL
    6b1af51b-1486-4af4-a1b8-a81d0384103e: 'Staff Position Title' # Staff Position Title
    06bbbbc1-a324-4d8f-a7ab-35ffdec4e3ad: 'Gap size' # Gap size
    6bf96f07-6131-4c8b-80e2-51bcb05f3a02: 'Company Name' # Company Name
    6c4d258b-d673-435d-ab44-9e23d5efd2c8: 'Richtext Basic' # Richtext Basic
    6c8f1949-c190-443d-802c-c3c1f5dc37d1: Simple # Simple
    6e1b6be1-3107-4814-a872-23f30cd8d17c: 'Image Preferences' # Image Preferences
    6e3bf4e6-dd92-4561-82a3-c3ddba133a3c: 'Alternative Phone' # Alternative Phone
    6e42b367-66ec-42ab-94fc-a483f9c3c0dd: 'Social Inspiration' # Social Inspiration
    6e2195b7-55a8-429f-bcc4-73c98efa3f75: 'Expo Sessions' # Expo Sessions
    6e6943d0-412b-41f5-9cec-dcfddb2a6c8a: 'Post Category' # Post Category
    6f06d019-72c3-4371-971f-914121623042: 'Site Identity' # Site Identity
    6f67a8a1-ceeb-413b-bd51-fcd1b7c8964b: 'Image List Captions' # Image List Captions
    6f255c3b-0320-4611-a933-0be2b9c7f380: 'Item Title' # Item Title
    07a39977-6446-4c8f-b573-201cbebbdeb4: 'Simple With Links' # Simple With Links
    7c7b9544-a011-4f62-aba1-5cd71386e411: 'Site Search' # Site Search
    7c86f1f0-5ae3-4c62-8604-6bbd1ab981ab: 'Newsletter Feature Card - Product' # Newsletter Feature Card - Product
    7cf65e4c-a1e8-430b-b9ef-b289751d770e: 'Generated PDfs' # Generated PDfs
    7d9a07fe-084f-4520-84c8-f73991962aac: Icon # Icon
    7d128647-d962-49a5-a00a-bd30feb5dae6: Ship # Ship
    7eb07469-1769-4028-b30f-6ccd331a751d: 'Consultant Files' # Consultant Files
    07eb55bb-21b5-488b-ac63-3a4c59595ff4: 'Full Width' # Full Width
    07fb62f2-435f-4cad-97f6-c0ba95cf91f6: 'Team Members' # Team Members
    8a1c0ad0-7e96-43a0-a0ba-61bfdabfdf8b: 'Banner Content' # Banner Content
    8a1ce942-fa35-4adc-bf2b-806fe175dfa7: Default # Default
    8a34a59c-d941-4815-9cbf-8d68ad54dfb8: 'System Icon' # System Icon
    08a55bd7-27b4-4c2f-b213-e2c825a6ba31: Logo # Logo
    8a535a21-e012-44f9-98c8-eb4104104b01: 'Show on Homepage' # Show on Homepage
    8a8888af-000e-4451-a668-0b2fcca66005: Date # Date
    8a227066-52db-42c9-92c4-1a06514bd6ec: Testimonials # Testimonials
    8b18b1ba-b9ba-4308-b4f8-bec8c72b9a42: Documents # Documents
    8b207736-1bfa-4d03-85a1-be66a2605a15: Product # Product
    8be355f4-8e66-4141-8056-4ae6b82d269c: Events # Events
    8d6d87ef-ca0b-48a9-abe5-6410b42ca443: Branch # Branch
    8df060d6-85a0-4d1f-81e8-4b3c8e0c37f2: Default # Default
    8df7491e-a8d2-4b22-85d4-9575480afb5c: Branch # Branch
    8e022768-bf63-43c1-ad96-2f193cd7f337: Pages # Pages
    8e4396bf-398c-44ae-92d2-e324f715b444: 'HTML addon' # HTML addon
    8f1be1cc-8792-4081-a744-f4a1b1aa6555: 'Page Gallery' # Page Gallery
    8fe0c286-0bcc-4db1-a3aa-ef295d9ecbf1: 'Banner Style' # Banner Style
    9c00e458-03c3-494f-9b86-e4c2876f7e7c: Large # Large
    09c062f8-e879-4077-9ff4-7f2249018f2d: 'Holiday Search' # Holiday Search
    9c3d2081-d0cf-46cd-b211-8948b82c4c22: 'Ship inspection Registration' # Ship inspection Registration
    9d61492e-294d-4529-8a53-8271c814f7b4: 'Children Pages Gallery Columns' # Children Pages Gallery Columns
    9de32916-169d-4cc3-9750-6021a6b31d8a: 'Top Nav' # Top Nav
    9e4c00d1-f99d-462a-948c-9caa68cccf07: 'Sans Media' # Sans Media
    9e6fc2a9-33bb-49de-90a3-8201c0d89de2: 'Platinum Cruise Club Newsletter' # Platinum Cruise Club Newsletter
    9f28f447-6b2b-4002-b018-1bc32f8a2a6f: Product # Product
    9f5405c6-c87a-4209-be93-67d7062f41b6: 'Column Sizes' # Column Sizes
    9fdcefe2-82ff-4f7e-9a4d-6790a1020c10: 'Order by' # Order by
    9ff82229-6197-484b-bc7f-d51bb8ba9196: 'Show on all Pages' # Show on all Pages
    10e6b150-acd7-4157-8371-c10e26d1bf9e: 'Expo Session Times' # Expo Session Times
    12ca3475-9685-48c5-96a7-980bfdcb097b: 'Dynamic Travel Type Combination' # Dynamic Travel Type Combination
    17a3a764-a4a2-45c5-adf1-8cf317752bfa: 'Travel Date Range/s' # Travel Date Range/s
    24ed30fc-1608-4a9d-80ee-e7d688eb0094: 'Website Product Booking Deposit' # Website Product Booking Deposit
    026e8d59-51b9-42a1-8e69-5ff6c51da7f3: Larger # Larger
    27dec05b-6868-4f28-a6bb-7020d3e5e34c: Associations # Associations
    32d5ef37-fc9b-4525-9da8-5f041a374e96: 'Attendees Limit Number' # Attendees Limit Number
    33af536c-137e-4f74-95bf-86dee2c32786: 'Newsletter Feature Card - Custom' # Newsletter Feature Card - Custom
    35b6b2b8-d71c-4bea-9be4-4703ef1ff644: Tagline # Tagline
    37b232e9-33ac-4270-ad05-bdc918c39d15: 'URL does not exactly match' # URL does not exactly match
    37dbf28e-fa55-4337-b4f3-d8ad0e96f90d: 'Icon & Text list' # Icon & Text list
    39c3595c-44e4-4c20-9957-ffd6e104c6d9: Suppliers # Suppliers
    39d099b7-c886-4280-b9c2-1229c80f9c23: 'Footer Contact Details Text' # Footer Contact Details Text
    40f44874-9e02-4580-ad0f-9d4ba41d6ab4: 'Hosts & Escorts' # Hosts & Escorts
    41c243e0-0b38-44be-a740-d0a6acb0416b: 'Product Categories' # Product Categories
    42c201e1-55df-4dda-b3ac-36b2ec5a7644: Event # Event
    44b09b95-5af1-4ad0-bf6a-33fcba79a156: 'Custom Price Description' # Custom Price Description
    44c0a283-b1b2-49c6-ab94-ec5ec812cbbd: 'Features Promotional Section' # Features Promotional Section
    44ef7fd6-a4b8-4a30-9a5b-8c4c3fc38ddf: 'Image List Display' # Image List Display
    46e7d17e-e2d8-47df-a465-564530c967cf: 'Event Session' # Event Session
    47a3b9dd-b788-44e3-98e5-c88e8dbcb728: 'Itinerary Section Title' # Itinerary Section Title
    47eb0935-0dfa-4954-83da-45e8f9444f7d: 'Admin-only Category' # Admin-only Category
    48a7a1e3-5280-4b74-bf54-c92aa3371768: 'PDF downloads Gallery' # PDF downloads Gallery
    49c89fd1-2934-418d-b7e8-82f38b985e87: Consultants # Consultants
    50f801f5-7b05-4299-80db-d53717884e7a: 'Target File' # Target File
    51c58fb1-fb1f-46df-b9d9-35b429089e14: Attribution # Attribution
    52b92093-bda6-49a8-a1e5-e859db6fa6de: 'Staff Position Title' # Staff Position Title
    52c8af19-4b86-4b3d-a6a2-41d07c5e03e0: Description # Description
    52c46373-96e9-460a-a5ba-eae1a9ea9fed: 'Branch Location Map' # Branch Location Map
    58a0197d-774d-40de-b9a3-3e9dceccd8c1: 'Richtext — Basic + Links' # Richtext — Basic + Links
    59d1e068-f1bc-499c-b8af-9bca74ebec23: 'Feature Background Colour' # Feature Background Colour
    59d2d476-7c7d-4d0e-8312-2413c6b84978: 'Submission Reporting Frequency' # Submission Reporting Frequency
    61d816f1-a8fe-4b66-a35b-9461992ae66f: 'Image List Display' # Image List Display
    61ecbd2b-5bfe-4bda-855f-e3d5b05a80e4: 'Text with 80 Character Limit' # Text with 80 Character Limit
    62bbd016-3797-4207-bc23-5bebf5e211e4: 'Image Gallery Preferences' # Image Gallery Preferences
    63f515ee-413e-4c0b-bbdb-b7022fb89d2a: Expo # Expo
    66f47572-900e-45a6-bfda-01a1892898fb: 'Embed Widget' # Embed Widget
    69b88b66-79dc-4aa2-b451-99b1a8664d18: 'Instagram URL' # Instagram URL
    69da0017-9a7b-4a0a-a99b-a804b7f5dcd1: 'Payment Reporting Enabled' # Payment Reporting Enabled
    69e281bd-3750-4826-be8c-430c911d2cfb: 'Product Categories No Hierarchy Selection' # Product Categories No Hierarchy Selection
    73a3e79b-c34c-4142-bd03-279a1c83c5a1: Align # Align
    73b31498-a83c-4f49-9e36-3494626d29dc: Brochures # Brochures
    73dbbbae-1b99-4272-9058-70beceb6e7e3: 'Cruising Categories' # Cruising Categories
    74d7d6ec-2638-48a6-9a06-c1fd9b6f7b1e: 'Standard Seminar Registration' # Standard Seminar Registration
    075a19c0-2826-447a-8ee7-2ca913ba7d06: 'Expando Trigger' # Expando Trigger
    75d071ae-43cc-47d2-801f-010950fcf6a0: 'Start & End Destination/s' # Start & End Destination/s
    76e44f5b-afea-4eac-b626-4d74c2187c1e: Organisations # Organisations
    76ee71f4-54e9-4241-8a38-f65d7062ffd8: Events # Events
    79a6132f-d52b-48c7-a0be-2d245f7e4b37: 'Team Members' # Team Members
    81d31ef9-650d-4515-8378-07e1aaa3920e: Alerts # Alerts
    84bc96a2-e050-417e-bbff-7976aa293e83: 'Date Range' # Date Range
    85cfe2cd-e627-4802-b4f5-3964ffc8f425: 'PHT Departments & Positions' # PHT Departments & Positions
    85f54dc3-f4ed-474f-b7cc-52c84b2f3b91: 'Related Links' # Related Links
    88da6c74-0b14-4006-8ce2-77db52c368e9: Videos # Videos
    88efa70d-5e25-4574-9aac-cf13f8a106f1: Testimonial # Testimonial
    89e7e7df-e72c-4b4d-9d61-1d8d8ac63698: Icons # Icons
    96f980b8-3019-4ac2-b0a0-d430196ac7e8: 'Team Members' # Team Members
    98a19402-4c39-4db5-8695-6fae842f14e4: 'Image List' # Image List
    98c2e58b-0203-4aa4-89af-4036d893dfca: 'Sidebar Addons' # Sidebar Addons
    121f29f5-7073-427b-9169-f7c8e3812911: 'Payment Reporting End' # Payment Reporting End
    179f1561-ce06-4387-bbdf-208df33bc851: 'Email Address' # Email Address
    193c0859-dda8-4ace-b87b-508ddef61c50: 'Text with line breaks' # Text with line breaks
    223d04fd-5d7a-47fd-8d39-84773548adb8: 'Accordion Expanding Content' # Accordion Expanding Content
    372d10b9-60bc-4454-bc4d-ff6512dafc1c: Airlines # Airlines
    390b848f-dcc4-4ddf-b63d-0b716186c340: 'Promo Card set' # Promo Card set
    442be152-73f6-4f28-a7a5-708b749dc6bd: 'Start Date' # Start Date
    0459d2ac-b3a4-478d-b16d-a343a8182dc7: 'Product Features' # Product Features
    503d02ec-5589-450c-b89a-fa49522207fd: Align # Align
    527debb4-c86a-488e-99ee-48102bc90cea: 'Expo Sessions' # Expo Sessions
    619e3abe-81d3-467e-837e-08ef4fd97c43: 'Promo Card' # Promo Card
    623db196-498d-4c4b-ab91-8d16301180f3: Images # Images
    624ce930-8ab6-4449-b8d5-c6bf5ee2d676: 'Display URLs - URL exact match - URL' # Display URLs - URL exact match - URL
    648a5ac8-9784-4fe8-a240-a6fec16c2b5e: 'Attendees Reporting' # Attendees Reporting
    654c13ef-d9c8-48da-a016-face18e616af: New # New
    698e0dea-d42f-4d91-8cba-2afb3ffabb03: 'Link Label' # Link Label
    715df3c6-7270-412f-9093-b8ebe0e7342f: 'Display when exit intent detection' # Display when exit intent detection
    744bfd72-7a02-4488-8ab1-4968865a2744: 'Window Content' # Window Content
    771d90ed-3e58-4e4b-8340-54e0af52cc39: 'Meta Keywords' # Meta Keywords
    778f7e21-a1f3-401a-8d2f-0ab09e8e6c5e: 'Modal Window' # Modal Window
    833fefac-ec51-4218-9aef-081f37a380ad: 'Cinema Location' # Cinema Location
    885db462-f60e-426f-ae66-1064f212dec6: 'PDF Download' # PDF Download
    889fbf54-2d72-4d83-bdbf-afd8450ec955: 'Data Entry' # Data Entry
    892ed559-6744-4a00-b468-90914932245d: 'LinkedIn URL' # LinkedIn URL
    902c8081-5f52-4843-b998-d0808ffc039b: 'Twitter Handle' # Twitter Handle
    903d8a27-ce40-4c29-b42c-77ae15b01435: 'Events Gallery' # Events Gallery
    1532a7cf-af10-4704-be65-ba3dcb6deed0: 'Internal Nav Slug' # Internal Nav Slug
    1693d210-dba1-495e-ad9a-5caf839651c6: 'Internal Link Target' # Internal Link Target
    1791bd24-9c56-4769-a77b-76cf50ec572c: Ship # Ship
    2035f56e-d643-44a0-99f5-02a2d747b2f0: 'Testimonial Display Style' # Testimonial Display Style
    2070eeff-aaa9-4ff4-ab7b-dceb1ff31aca: 'Show Contact Details' # Show Contact Details
    3142c2fc-2505-40de-9658-4a3f4bb418fd: 'Panel Settings' # Panel Settings
    4109af57-b2b7-4e90-a63c-9dc99d85bf95: 'Advertised Specials' # Advertised Specials
    4248e721-e035-4bf5-9f20-c2e65c57720a: Time # Time
    04946e90-ce5b-46d8-a5e1-8995973e9247: 'Price Description' # Price Description
    5626d44e-de59-4cd4-8813-d7a7b70dbe5b: 'Image Caption' # Image Caption
    5731f108-a53e-4151-8bd9-90209d1c39f0: Products # Products
    5976b6b4-6c2a-4ea8-a27c-75b9f3bd5619: 'Background Colour' # Background Colour
    6165fcc8-794b-4705-b9fa-cea791a8acc4: 'Mobile Order' # Mobile Order
    6233a6ac-70c4-4e76-a0e4-a76a71971a16: Form # Form
    6510acd6-4335-46ed-8c65-b58d2b449656: 'Inner Page' # Inner Page
    6849b023-bb8b-4592-882f-478dd9aa06d8: 'Target Slug' # Target Slug
    6861d633-c4ee-455a-9fe8-33723205a8e1: 'Team List' # Team List
    07949f0d-18c2-4dd7-af8b-1b3c5b12f65f: 'Mint Payments Company Token' # Mint Payments Company Token
    8215d7d5-0ef6-4a7c-82f6-321ac2d0adf0: 'Generic On/Off' # Generic On/Off
    8871e52a-b507-4cc8-817f-2b4304acbf21: Train # Train
    9023f5e8-3607-4826-af7c-85d4e7a9f422: 'Blog Posts' # Blog Posts
    009535ab-0ea6-4fae-bcc3-953b5901a633: 'Submission Summary' # Submission Summary
    12964fb8-f40f-4aed-920d-70219f4e290c: 'X/Twitter URL' # X/Twitter URL
    22930cee-3c90-4505-bd3d-6a541411dcc6: 'URL exact match' # URL exact match
    30142b0a-47fb-445e-9255-db892c3687b4: 'Accordion Expanding Content' # Accordion Expanding Content
    34344df6-73da-4403-afd2-beda8a573d0c: 'Phone Number' # Phone Number
    39208a21-0fa8-4299-b125-86dfa90eb907: Caption # Caption
    42091c19-83b9-4224-b75d-8a5227d5d2e4: 'Image Captions' # Image Captions
    52428e0b-f88a-4ba4-a3cc-1f74c262b6f0: 'Sprout ID' # Sprout ID
    66516f80-5577-4e41-aff5-a935d6d48e15: 'Consultant Login' # Consultant Login
    71718f7a-b09d-4ff0-b9e9-5d3748a8b5f1: 'Dinner Event Registration' # Dinner Event Registration
    73022d75-9a0c-4060-a764-4a3de18eab90: 'External Nav Url' # External Nav Url
    89303a1b-8fec-4e93-9b39-9b5d6f2d4f22: Excerpt # Excerpt
    104386b3-53d9-4dbd-8dab-7c3034d5deb3: 'Column Gap' # Column Gap
    458697d4-d915-4838-bf5f-40ce515f5b6f: 'Search Type' # Search Type
    465145dd-3bd1-4710-927e-28ed6b4aca6e: 'Background Colour' # Background Colour
    507895a4-f036-4c53-beb0-b4f9533ed488: Link # Link
    512550b6-a881-4d50-88f9-9887d1d674cb: 'Display URLs - URL does not exactly match - URL' # Display URLs - URL does not exactly match - URL
    679134c7-277f-4780-89d9-74f0e44d7938: 'Target Url' # Target Url
    692981e9-ded2-4931-bee6-40070fe65c66: Subtitle # Subtitle
    3219033f-a48a-4edf-a481-a910135902f0: 'Add a Children Pages Gallery' # Add a Children Pages Gallery
    8601272c-2a4a-474e-878f-632387feb219: Branches # Branches
    32149112-f06d-4152-8730-eedec0229fec: 'Mint Payments' # Mint Payments
    55507305-f580-44dd-a4f5-6657b10c4ffc: 'Page Add-on' # Page Add-on
    55661050-1f72-4312-b11f-bc1556cf56ab: Layout # Layout
    66173656-654f-4f80-ad48-d41b71711f1f: 'Delay in seconds after page load' # Delay in seconds after page load
    69539505-f83a-4f23-8166-5bc13243c275: Sitemap # Sitemap
    72672597-9ead-446f-9839-b7cd1f157bbc: 'PHT Terms and Conditions' # PHT Terms and Conditions
    76510527-4553-4ff1-892e-df6def007821: ABN # ABN
    79218843-e921-41f8-b063-1e10da1d04a8: 'Expando Item' # Expando Item
    87158675-0679-47c6-a8c1-6a303243640a: 'Contact Details' # Contact Details
    a0cc0ce2-ff9c-472f-ae65-4dd54ccd46f4: 'Background Colour' # Background Colour
    a0561b1c-9595-4250-988b-729519a60881: Layout # Layout
    a1f21454-87f1-4022-ad8f-81a927d25ec4: 'Standard Newsletter Feature' # Standard Newsletter Feature
    a4edf67b-5ec6-4e48-bf6d-f74ea56bc381: 'Payment Receipt' # Payment Receipt
    a8ac4293-1803-4a7a-b059-765ccf017923: 'Page Redirect' # Page Redirect
    a9c2ca99-f8fe-44af-a6a5-3b00e9cc237d: 'Open in New Window' # Open in New Window
    a9dbf427-c0d4-4887-a76c-a8f9e660c4b8: 'Image Columns' # Image Columns
    a9fa4bce-4bdd-4816-bd77-97a96602fd81: Home # Home
    a50bc021-3e5e-42b8-a5c6-33b4c6f64b5f: 'Display URLs - URL contains - URL' # Display URLs - URL contains - URL
    a75fbb07-9213-4d70-b72b-3ab18a101016: 'Products Gallery' # Products Gallery
    a79b588d-20cf-458c-adbf-aa3c17d9dcec: Attribution # Attribution
    a82b6c48-18b4-4ea6-bdcb-22cd288e1768: 'Blog Posts' # Blog Posts
    a95cd64c-7043-4819-94c5-81ce8a5b272a: Departments # Departments
    a96d411c-be3a-4073-be8c-d676c99ecb01: Banner # Banner
    a151631e-3eba-46d2-96b5-726046a06c01: 'Branch Opening Hours' # Branch Opening Hours
    a6715600-a3f0-4267-beb5-4a645b0a2e24: Price # Price
    aaca8b2a-d03c-494a-a029-66f1758ac4d7: 'Columns Abreast' # Columns Abreast
    ac05fa5c-7805-4014-acfd-5bd1e2967801: 'Content Highlight' # Content Highlight
    ac5ddd1c-567b-4873-bfd8-16b776c59e19: 'Branches Listing' # Branches Listing
    ac337b81-4cee-4685-afc9-959f66a13b11: '2 Column Richtext' # 2 Column Richtext
    ad88eac8-e72b-4936-b35d-353465bce2bc: 'Site Identity' # Site Identity
    ad2295ee-2dd4-4c3a-9478-3d2d4602c42a: Branches # Branches
    adefd800-f259-4451-8961-a564a9656f35: Train # Train
    ae191663-ca2d-42f6-b530-92a2898a6dcf: 'Icon + Text' # Icon + Text
    aeca0ea7-c5c6-4ceb-a578-a4d1f29a3c7e: 'Page Banner Style' # Page Banner Style
    aee71604-1de4-4e45-8f80-01466c1d5451: 'Map Image' # Map Image
    af862299-4b42-4be7-b335-aa89e6372f7f: 'Standard newsletter' # Standard newsletter
    afb06c07-eee2-4c2f-a623-7c5976c82755: 'Meta Title' # Meta Title
    afc87b60-c29a-44c2-b706-75f3739c05f5: 'Link Field with Label' # Link Field with Label
    b0f64563-96cd-4f5a-9b96-a2db34e12866: 'Mobile Order' # Mobile Order
    b3f6f237-ba18-46b4-8ee5-f09a037e5ab4: 'Payment Reports' # Payment Reports
    b4b19fbc-230c-44a1-b7ed-d1f0af8eace7: 'Video Url' # Video Url
    b4f276a2-9d39-4a2d-b047-7e134f9a0459: 'Availability dates type' # Availability dates type
    b5f1c7b2-2601-4f1e-996f-1fbbadb7e646: Address # Address
    b7fb1de6-ba22-4bc9-94d8-d4e510c207a2: 'Travel Dates' # Travel Dates
    b58d0585-f0af-4b67-9172-d3ea303a75c4: Destinations # Destinations
    b90e9dd8-9324-47ba-982f-76a1f39c2c05: 'Inclusion Combination' # Inclusion Combination
    b470d9c4-c913-4b59-9916-9122703a1b98: 'Online booking deposit' # Online booking deposit
    b879ff1f-9d6f-4acf-881d-7cecf2186b3c: Organisation # Organisation
    b1488d78-b0ba-4163-96be-b976c2bb0a96: 'Event session times' # Event session times
    b8933ead-865e-4214-ada0-b10ee544126f: External # External
    bb505b4c-b5d5-4534-afd1-b6c5a30e7eff: 'Team Member' # Team Member
    bc97b5c2-ea46-4e9e-b7ad-524f2f923693: Airfares # Airfares
    bce133b8-d726-4c05-a7c2-9353d45e3b86: 'Richtext — Standard' # Richtext — Standard
    bd56dc57-8c8e-4431-a202-4378edcc2c72: 'Public Schema' # Public Schema
    bd7114d4-a7f6-445c-8190-b1653ded024a: Mailchimp # Mailchimp
    bec30a08-3b3c-4d05-80ae-fb17f8f4344f: Destinations # Destinations
    bf55692e-49e4-4124-9250-18ec8824aafb: Newsletters # Newsletters
    bfb19cb5-8105-4c0d-92e0-70934b26e830: 'Post Category' # Post Category
    c0da8ceb-dfbf-49b6-a01d-8b027afa628b: 'Fixed Deposit Price' # Fixed Deposit Price
    c2b8f887-9c81-46f3-9d2b-ffca4ba8f463: Promos # Promos
    c4f228bf-26a7-4d00-96ba-af6869741de6: 'Deals Offered' # Deals Offered
    c5b52d2a-9fbe-4afe-830f-e3917a248d53: 'Expo/Multi-Session Registration' # Expo/Multi-Session Registration
    c8b583af-2579-4a45-b50c-a9d37005074a: 'Panel Width' # Panel Width
    c21ca1df-6339-4dc6-9836-8c5e51908f8c: 'Profile photo' # Profile photo
    c22fd2c3-2678-4e83-ac52-7bc7d6db3af0: 'Panel Layout' # Panel Layout
    c32b5716-0c09-4b9b-ad85-70e711f93c99: 'Logo Default' # Logo Default
    c134b6e9-fab9-4fb5-95a2-b830e442e6d6: 'Internal Slug' # Internal Slug
    c345d6df-715a-475d-b0b3-f48a8d61099d: 'Blog Categories' # Blog Categories
    c354e285-02ba-4299-8608-ada47eb679fd: 'Attendees Limit Enabled' # Attendees Limit Enabled
    c8797bc2-f47e-49c3-9142-a6416e534b82: 'Products with this Category' # Products with this Category
    c5160052-d1a7-484b-b806-ae14f1450a07: 'File Link' # File Link
    ca688c53-4fa3-4026-a0bb-4d86bb5ac683: 'Link Helper' # Link Helper
    cb2b6bb6-014f-4967-b1b9-cbf8543fa26f: 'PHT Department' # PHT Department
    cb2697da-84c5-4e47-a1e3-2948144668ec: Blockquote # Blockquote
    cbd3a360-6d38-4b59-977b-07cf1b6c8a3e: 'Link Helper Solo' # Link Helper Solo
    cc9d5844-52b4-42df-b505-9f210c3902c6: 'Alert Message' # Alert Message
    cd827c21-5686-4d89-bb58-7e9bd547703a: 'Content Source' # Content Source
    cdc958b8-d44e-46af-b762-566867461436: Supplier/s # Supplier/s
    cdfdb7f9-0d2a-40de-a87b-0313e4051de0: Form # Form
    cfa89268-6a51-439c-a16f-c133c3164422: 'Bitmap Icon' # Bitmap Icon
    d022a44f-44ad-4a43-bc57-8190b2f331e4: 'Image Ratio' # Image Ratio
    d3b549c4-162b-4593-b538-c7be8dc9d782: 'Holidays Search' # Holidays Search
    d6b2cba9-666a-4f09-acb1-3033e0314580: 'Host or Escort' # Host or Escort
    d6bc72fa-3e2a-468a-aa9f-5b1877dd57d3: 'Simple Date Range' # Simple Date Range
    d16c7dca-54d9-4432-930b-f5687bf25ea1: 'Seperating Line' # Seperating Line
    d764e887-661d-4a7c-a171-7434a31e4cdd: 'Session Times' # Session Times
    d3449bc2-aade-4d83-ad0b-261af8f832fc: 'Inclusions Section Title' # Inclusions Section Title
    d7352d39-aba5-4bc8-8168-2e7ade584eec: 'Days to wait to show again' # Days to wait to show again
    d4634097-1ca4-476e-9956-0c70da055839: Alignment # Alignment
    dab487b8-f8f9-4541-91ab-ccaa9e5fc503: 'Column Gap' # Column Gap
    dcd9a0c7-ecb6-43cf-b4ad-85cfeb338918: 'Ribbon Alert' # Ribbon Alert
    dcf36b34-aafa-4054-bb23-351c92abe548: 'Short Description Blurb' # Short Description Blurb
    dd77fc00-9cf0-4396-b3c2-ecf8c1b4cc93: 'Alert Show' # Alert Show
    dea9fa44-3419-47e4-9824-82b405e078b6: Brochures # Brochures
    df43dd77-3870-4093-9a31-6cf08235139f: Airport # Airport
    e02b242d-8d5a-4693-a7a4-deddaee5bfb0: 'Display URLs' # Display URLs
    e3fc8a4c-fd5c-4594-b702-beb33e41bda3: 'Airfare Class' # Airfare Class
    e6ca58d7-6008-4434-a60f-87829578d827: 'Canonical URL' # Canonical URL
    e6cf7a49-c3b8-48a3-b530-98a087429cda: 'Add-on Item' # Add-on Item
    e6d12075-995f-4bfd-bbcd-ad18ff57d988: E-news # E-news
    e8f65c97-ebf7-491f-9568-6a219a00d3fd: 'Expando Content' # Expando Content
    e10f6c57-6369-4859-8da9-285adef5ba76: 'Travel Type Inclusions' # Travel Type Inclusions
    e34b3b87-3ffa-4cc2-8d0d-6694dcc5b43b: 'Internal Nav Entry' # Internal Nav Entry
    e43d9f86-3721-4231-a956-77872157b77d: 'Bottom Bar Addons' # Bottom Bar Addons
    e74b367f-00bb-4e22-bf47-257429d4dd0e: 'Google Map Link' # Google Map Link
    e92b437b-f819-453d-aa47-d19587e627db: 'Small Text and Links' # Small Text and Links
    e575a663-1122-4341-90e3-fdca7612a9a5: 'Image Ratio' # Image Ratio
    e953ecae-a41b-4a75-8bd8-fe7d87f2e035: Destination # Destination
    e614730f-0e9d-475d-be9d-98131c01126c: 'Product Categories' # Product Categories
    e6626276-093e-46d3-9e56-deb284e72876: 'Show Hide' # Show Hide
    eb77e0ef-411b-4e7a-8388-bb0a903fb21f: 'Flyer or Brochure' # Flyer or Brochure
    ebc45aee-24a8-4c5d-8435-8d961d154c97: 'Page Add-ons' # Page Add-ons
    ed08dded-d02c-4a15-a218-b5e7b8370a35: 'Enable Sidebar' # Enable Sidebar
    ed5e1997-be24-46fa-9410-026c83ea2716: 'Brochures Gallery' # Brochures Gallery
    ed8be091-9ea6-41fb-a7db-2c375dfbef2f: 'Campaign Useage' # Campaign Useage
    ed8655a6-ecee-4828-b2f8-29272c76d528: 'YouTube URL' # YouTube URL
    edd80c85-d160-4007-9f0e-284c5f68f27f: 'Primary Navigation' # Primary Navigation
    ee46dd7f-8e89-496f-94c4-8fe22ad22bc5: 'Client Type' # Client Type
    ee318474-ed6f-4ce5-8d21-b9f8dc1c8316: Richtext # Richtext
    f0f4d5a7-13e8-4bea-963c-7da70aa715a9: Copy # Copy
    f07d3148-b9d2-4445-b6e3-f8c3bf54b435: 'Airfares Gallery' # Airfares Gallery
    f2ebb14a-0569-49a5-b05f-fbe18f891850: 'Parent Page' # Parent Page
    f4be04ad-8d62-4f09-8770-58c724d99ed7: 'Payment Reporting Email Address' # Payment Reporting Email Address
    f7dfa0b2-e921-48d9-adcb-5432926b474f: 'Richtext — Sans Media' # Richtext — Sans Media
    f8deab38-ac24-463d-be3d-7e56628605f8: Slug # Slug
    f8ee5efa-7a31-4482-a9ce-5b1698e95f74: 'Attendees Reporting' # Attendees Reporting
    f38b47ce-63bc-4c52-9aa0-b3d982a67870: 'Client Event Deposit/Payments' # Client Event Deposit/Payments
    f72a726f-8e61-4212-9385-c97da1b91374: 'Product Category Travel Type Inclusion' # Product Category Travel Type Inclusion
    f76d5813-c986-4e10-bf0d-ec4a9537493b: Footer # Footer
    f85a92fb-60aa-4278-84f7-0e9c0d7765f2: 'Meta Description' # Meta Description
    f369a2c5-160c-4e43-97fa-133cc6831407: 'End Date' # End Date
    f4003fc2-46de-4abc-b5f6-a5e9f612bf9e: '2 Column layout' # 2 Column layout
    f14564db-2c06-4152-9e89-b0fa785d6751: 'PDF Downloads' # PDF Downloads
    f34874a3-c925-46da-a4cf-2a26d4ceb67d: 'Additional pages' # Additional pages
    f48377d0-5444-4752-bef1-41af3daf508d: Testimonials # Testimonials
    f74308a1-642a-4b5b-b6d8-0c93a1966d10: Small # Small
    f931012f-5a74-466a-bd1e-ec92b09ebbf6: 'Link Icon' # Link Icon
    fa83bae9-477d-4545-b101-e4ba7a774376: 'Product Filters' # Product Filters
    fae7c2a6-d215-4502-874a-6b077c2afa1d: Default # Default
    fc09b0f5-b91a-4793-8963-f186423aa069: 'Payment Reporting Start' # Payment Reporting Start
    fc2a3f5e-eb9b-49a9-ba03-6fa3e62dd843: Redirect # Redirect
    fc30c24c-5437-4393-9303-77c98e09766a: 'Destinations Gallery' # Destinations Gallery
    fcb6fd6a-e9fd-4794-a829-8a2e7cf8993c: Team # Team
    fcc8d59a-9f6a-4005-90b4-00cb1d3e4294: 'Alert Theme' # Alert Theme
    fdde5e27-6d59-4bb2-88ad-ff6fad998cf8: 'Vertical Alignment' # Vertical Alignment
    fe4ff3ac-df3d-432d-82b0-fb56d79a5862: Destinations # Destinations
    fe544e7d-de28-45db-924a-b3551bdf4fea: 'Call to Action Boxes' # Call to Action Boxes
    fee938c0-229a-4cee-b558-fa12df41dc4b: Medium # Medium
    ff0889d1-9f9e-4a53-866a-5e007f54f7dc: 'Inclusion Combinations' # Inclusion Combinations
    ff2d6a06-c3fe-482e-8578-2b620bcd5deb: 'Display URLs - URL does not contain - URL' # Display URLs - URL does not contain - URL
    ffa5d4df-3c7d-4529-97fb-d23c96832293: Brochure # Brochure
plugins:
  aws-s3:
    edition: standard
    enabled: true
    schemaVersion: '2.0'
  calendar-links:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  ckeditor:
    edition: standard
    enabled: true
    schemaVersion: 3.0.0.0
  cloner:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  colour-swatches:
    edition: standard
    enabled: true
    schemaVersion: 1.4.3
    settings:
      colors:
        -
          __assoc__:
            -
              - label
              - '#001F5b'
            -
              - color
              - '#001F5b'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#008FC6'
            -
              - color
              - '#008FC6'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#00A7B6'
            -
              - color
              - '#00A7B6'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#00AEEF'
            -
              - color
              - '#00AEEF'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#00B8BC'
            -
              - color
              - '#00B8BC'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#013E7F'
            -
              - color
              - '#013E7F'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#01A371'
            -
              - color
              - '#01A371'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#0B7454'
            -
              - color
              - '#0B7454'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#241F20'
            -
              - color
              - '#241F20'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#247485'
            -
              - color
              - '#247485'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#248E39'
            -
              - color
              - '#248E39'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#28A5B2'
            -
              - color
              - '#28A5B2'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#471463'
            -
              - color
              - '#471463'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#58128F'
            -
              - color
              - '#58128F'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#601568'
            -
              - color
              - '#601568'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#6BCBD9'
            -
              - color
              - '#6BCBD9'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#6DCFF6'
            -
              - color
              - '#6DCFF6'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#74381B'
            -
              - color
              - '#74381B'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#7AC143'
            -
              - color
              - '#7AC143'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#80298F'
            -
              - color
              - '#80298F'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#87D1D2'
            -
              - color
              - '#87D1D2'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#8f7e59'
            -
              - color
              - '#8f7e59'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#9A191E'
            -
              - color
              - '#9A191E'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#A1238C'
            -
              - color
              - '#A1238C'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#A48F69'
            -
              - color
              - '#A48F69'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#A67339'
            -
              - color
              - '#A67339'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#A9225C'
            -
              - color
              - '#A9225C'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#C40002'
            -
              - color
              - '#C40002'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#C85D1E'
            -
              - color
              - '#C85D1E'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#CD1F29'
            -
              - color
              - '#CD1F29'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#CF9F51'
            -
              - color
              - '#CF9F51'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#D66CAA'
            -
              - color
              - '#D66CAA'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#DC5625'
            -
              - color
              - '#DC5625'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#E60E62'
            -
              - color
              - '#E60E62'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#ED6809'
            -
              - color
              - '#ED6809'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#EE4444'
            -
              - color
              - '#EE4444'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#EE4C98'
            -
              - color
              - '#EE4C98'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#EF8B22'
            -
              - color
              - '#EF8B22'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#F31812'
            -
              - color
              - '#F31812'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#FDB515'
            -
              - color
              - '#FDB515'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#FDB945'
            -
              - color
              - '#FDB945'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#FFD400'
            -
              - color
              - '#FFD400'
            -
              - default
              - ''
            -
              - class
              - ''
        -
          __assoc__:
            -
              - label
              - '#FFDB09'
            -
              - color
              - '#FFDB09'
            -
              - default
              - ''
            -
              - class
              - ''
      default: null
  cpips:
    edition: standard
    enabled: true
    schemaVersion: 1.0.3
  errorlog:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
    settings:
      enableJsCp: false
      enableJsSite: false
      enableServer: true
      expiry: '1 month'
      purgeInterval: 10
      siteUid: 6a8cfbbf-aee9-4f8c-ac51-e4bfe60d6879
  feed-me:
    edition: standard
    enabled: true
    schemaVersion: 5.1.1
  formie:
    edition: standard
    enabled: true
    licenseKey: 3O0C220X341DA9HI54FU2WSY
    schemaVersion: 3.4.8
    settings:
      ajaxTimeout: 20
      alertEmails:
        -
          - 'Jack Edson'
          - <EMAIL>
      captchas:
        __assoc__:
          -
            - recaptcha
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Recaptcha
                -
                  - enabled
                  - '1'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - recaptcha
                      -
                        - secretKey
                        - $RECAPTCHA_SECRET_KEY
                      -
                        - siteKey
                        - $RECAPTCHA_SITE_KEY
                      -
                        - type
                        - v3
                      -
                        - size
                        - normal
                      -
                        - theme
                        - light
                      -
                        - badge
                        - bottomright
                      -
                        - language
                        - auto
                      -
                        - minScore
                        - 0.2
                      -
                        - scriptLoadingMethod
                        - asyncDefer
                      -
                        - enterpriseType
                        - score
          -
            - hcaptcha
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Hcaptcha
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - hcaptcha
                      -
                        - size
                        - normal
                      -
                        - theme
                        - light
                      -
                        - language
                        - en
                      -
                        - minScore
                        - 0.5
                      -
                        - scriptLoadingMethod
                        - asyncDefer
          -
            - friendlyCaptcha
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\FriendlyCaptcha
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - friendlyCaptcha
                      -
                        - language
                        - en
                      -
                        - startMode
                        - none
          -
            - turnstile
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Turnstile
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - turnstile
                      -
                        - scriptLoadingMethod
                        - asyncDefer
                      -
                        - theme
                        - auto
                      -
                        - size
                        - normal
                      -
                        - appearance
                        - always
          -
            - duplicate
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Duplicate
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - duplicate
          -
            - honeypot
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Honeypot
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - honeypot
          -
            - javascript
            -
              __assoc__:
                -
                  - type
                  - verbb\formie\integrations\captchas\Javascript
                -
                  - enabled
                  - '0'
                -
                  - saveSpam
                  - true
                -
                  - settings
                  -
                    __assoc__:
                      -
                        - handle
                        - javascript
      defaultDateDisplayType: calendar
      defaultDateTime: null
      defaultDateValueOption: ''
      defaultEmailTemplate: default
      defaultFileUploadVolume: ''
      defaultFormTemplate: ''
      defaultInstructionsPosition: verbb\formie\positions\AboveInput
      defaultLabelPosition: verbb\formie\positions\AboveInput
      defaultPage: forms
      emptyValuePlaceholder: 'No response.'
      enableBackSubmission: true
      enableCsrfValidationForGuests: true
      enableLargeFieldStorage: false
      enableUnloadWarning: true
      filterIntegrationMapping: true
      includeDraftElementUsage: false
      includeRevisionElementUsage: false
      maxIncompleteSubmissionAge: 30
      maxSentNotificationsAge: 30
      pdfPaperOrientation: portrait
      pdfPaperSize: letter
      pluginName: Formie
      queuePriority: null
      saveSpam: true
      sendEmailAlerts: true
      sentNotifications: true
      setOnlyCurrentPagePayload: false
      spamBehaviour: showSuccess
      spamBehaviourMessage: ''
      spamEmailNotifications: false
      spamKeywords: ''
      spamLimit: 500
      submissionsBehaviour: all
      useCssLayers: false
      useQueueForIntegrations: true
      useQueueForNotifications: true
      validateCustomTemplates: true
  fractal:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
    settings:
      componentPath: '@root/fractal/_patterns'
      handlePrefix: '@'
      statuses:
        - ready
  google-maps:
    edition: standard
    enabled: true
    licenseKey: D0JI858K9DI2KWYMUK0W2ESI
    schemaVersion: 4.6.0
    settings:
      browserKey: AIzaSyBCetvgmYaiLlmdlczWrDrDMyXupelNF08
      enableJsLogging: true
      fieldControlSize: 27
      geolocationService: null
      ipstackApiAccessKey: null
      maxmindLicenseKey: null
      maxmindService: null
      maxmindUserId: null
      minifyJsFiles: false
      serverKey: AIzaSyDDbHFsCTWpmugFitTL9QtJt673BjugKZM
  kbdev:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
    settings:
      enableLogs: '1'
      enableMail: '1'
      enableSql: '1'
  oembed:
    edition: standard
    enabled: true
    schemaVersion: 1.0.1
  pdf-transform:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
    settings:
      imageFormat: jpg
      imageQuality: '95'
      imageResolution: '72'
      imageVolume: '5'
      page: '1'
  retour:
    edition: standard
    enabled: true
    licenseKey: Z5R4TV7A7PDVPYIPTLGD59XS
    schemaVersion: 3.0.12
  reverserelations:
    edition: standard
    enabled: true
    schemaVersion: 1.0.1
  seomatic:
    edition: standard
    enabled: true
    licenseKey: VBPUGPDBZ5Y0W9I75CYSWG8Y
    schemaVersion: 3.0.13
  sprig:
    edition: standard
    enabled: true
    schemaVersion: 1.0.1
  store-hours:
    edition: standard
    enabled: true
    schemaVersion: 1.0.2
  translate:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  vite:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
system:
  edition: pro
  live: true
  name: 'Phil Hoffmann Travel'
  retryDuration: null
  schemaVersion: *******
  timeZone: Australia/Adelaide

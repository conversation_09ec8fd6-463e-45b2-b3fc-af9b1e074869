<?php
/**
 * Vite plugin for Craft CMS
 *
 * Allows the use of the Vite.js next generation frontend tooling with Craft CMS
 *
 * @link      https://nystudio107.com
 * @copyright Copyright (c) 2021 nystudio107
 */

use craft\helpers\App;

/**
 * Vite config.php
 *
 * Based on the template from craft-vite plugin repo
 * (i.e. vendor/nystudio107/craft-vite/src/config.php)
 *
 * This file is multi-environment aware as well, so you can
 * have different settings groups for each environment, just
 * as you do for 'general.php'
 */

$isDevMode = App::env('ENVIRONMENT') === 'dev';

$serverConfigJson = '/vite/server.config.json';
$serverConfigPath = App::env('CRAFT_BASE_PATH') . $serverConfigJson;

$useDevServer = false;
$devServerPublic = null;

$internalHost = "host.docker.internal";

// The key/value pairs are programmatically updated in vite/startDevServer.ts.
// When started, the dynamically allocated port is written to server.config.json
// and we fetch the site code. When the process ends/is killed, these reset
// back to their default values (i.e. port will be set to null).

// A power outage or other unexpected termination (killed terminal) will result
// in a stale port value being present in server.config.json. As such, when we
// are in a dev environment, we double check that the dev server is running at
// the specified port and whether or not the local sitecode matches the remote one
// to prevent cross-site contamination.
if ($isDevMode && file_exists($serverConfigPath)) {
    $serverConfig = json_decode(file_get_contents($serverConfigPath), true);

    if (isset($serverConfig['port'])) {
        $useDevServer = true;

        $isSSLEnabled = getenv('SITES_SSL_ENABLED');
        $devServerPublic = ($isSSLEnabled === 'true' ? 'https://' : 'http://') . "{$internalHost}:{$serverConfig['port']}";
    }

    // Test whether the dev server running matches the project's site code OR
    // if the dev server is running at all at the port specified in the config.
    // The only time this *won't* be the case is if server.config.json contains
    // stale details from a previous dev server instance that was terminated unexpectedly
    if ($devServerPublic) {
        // Check the server.config.json file at the server config port:
        // Given this is all on loopback, receiving a "Connection Refused"
        // should be near instant in cases where the dev server is not running.
        $response = file_get_contents($devServerPublic . $serverConfigJson, false);

        if ($response) {
            $devServerSiteCode = json_decode($response, true)['site'];
            $craftSiteCode = getenv('SITES_DB_DATABASE');

            if ($devServerSiteCode != $craftSiteCode) {
                // Welp - server.config.json is stale. Set useDevServer to false!
                // This only gets solved when a new dev server for this site is started
                $useDevServer = false;
            }
        } else {
            // i.e. dev server is not running at all
            $useDevServer = false;
        }
    }
}

$siteUrl = App::env('DEFAULT_SITE_URL');

if ($siteUrl) {
    // This will be in format http://crftkb.jack.bunnysites.com/
    // so instead of removing trailing /, parse it as URL and use its components
    $siteUrl = parse_url($siteUrl);
    $devServerPublic = "//{$siteUrl['host']}:{$serverConfig['port']}";
} else {
    $devServerPublic = "//localhost:{$serverConfig['port']}";
}


return [

    /**
     * @var bool Should the dev server be used?
     */
    'useDevServer' => $useDevServer,

    /**
     * @var string File system path (or URL) to the Vite-built manifest.json
     */
    'manifestPath' => '@webroot/dist/.vite/manifest.json',

    /**
     * @var string The public URL to the dev server (what appears in `<script src="">` tags
     */
    'devServerPublic' => $devServerPublic,

    /**
     * @var string The public URL to use when not using the dev server
     */
    'serverPublic' => App::env('PRIMARY_SITE_URL') . '/dist/',

    /**
     * @var string|array The JavaScript entry from the manifest.json to inject on Twig error pages
     *              This can be a string or an array of strings
     */
    'errorEntry' => 'vite/error.ts',

    /**
     * @var string String to be appended to the cache key
     */
    'cacheKeySuffix' => '',

    /**
     * @var string The internal URL to the dev server, when accessed from the environment in which PHP is executing
     *              This can be the same as `$devServerPublic`, but may be different in containerized or VM setups.
     *              ONLY used if $checkDevServer = true
     */
    'devServerInternal' => '',

    /**
     * @var bool Should we check for the presence of the dev server by pinging $devServerInternal to make sure it's running?
     */
    'checkDevServer' => false,

    /**
     * @var bool Whether the react-refresh-shim should be included
     */
    'includeReactRefreshShim' => false,

    /**
     * @var bool Whether the modulepreload-polyfill shim should be included
     */
    'includeModulePreloadShim' => true,

    /**
     * @var string File system path (or URL) to where the Critical CSS files are stored
     */
    'criticalPath' => '@webroot/dist/criticalcss',

    /**
     * @var string the suffix added to the name of the currently rendering template for the critical css file name
     */
    'criticalSuffix' => '_critical.min.css',
];

{% extends '_layouts/cp' %}
{% import "_includes/forms" as forms %}

{% set title = type == 'expo' ? "Expo Registration Reports" : "Event Registration Reports" %}

{% block content %}

<h2>{{ title }}</h2>

{% set entries = craft.entries()
    .section('events')
    .type(type)
    .status(null)
    .all()
%}

{% if entries is empty %}
    <p>There are no active {{ type }} entries at this time.</p>
{% else %}
    {# Pre-compute display dates for efficiency #}
    {% set entriesWithDates = [] %}
    {% for entry in entries %}
        {% if entry.type == 'expo' %}
            {% set displayDate = entry.date %}
        {% else %}
            {# We don't have a direct 'date' field for events, so we base it on the earliest session time date instead #}
            {% set sortedSessions = entry.eventSessionTimes ? entry.eventSessionTimes|sort((x, y) => x.date <=> y.date) : [] %}
            {% set displayDate = sortedSessions|length > 0 ? sortedSessions|first.date : null %}
        {% endif %}
        {% set entriesWithDates = entriesWithDates|merge([{
            entry: entry,
            displayDate: displayDate
        }]) %}
    {% endfor %}

    {% set sortedEntries = entriesWithDates|sort((a, b) =>
        (b.displayDate ?? b.entry.dateCreated) <=> (a.displayDate ?? a.entry.dateCreated)
    ) %}

    {% set paramEntryId = craft.app.request.getParam(type ~ 'Id') %}
    {% set foundEntry = sortedEntries|filter(item => item.entry.id == paramEntryId)|first %}
    {% set selectedEntry = foundEntry ? foundEntry : sortedEntries|first %}

    {% set entryOptions = [
        {
            label: 'Select ' ~ type|capitalize ~ ' Entry',
            value: '',
            disabled: true
        },
    ] %}

    {% for item in sortedEntries %}
        {% set entryOptions = entryOptions|merge([{
            label: item.entry.title ~ ' (' ~ (item.displayDate ? item.displayDate|date : 'unknown date') ~ '|status: ' ~ item.entry.status ~ ')',
            value: item.entry.id,
            disabled: false,
        }]) %}
    {% endfor %}

    <form>
        {{ forms.select({
            label: 'Select ' ~ type|capitalize ~ ' Registration Form',
            name: type ~ 'Id',
            options: entryOptions,
            value: selectedEntry.entry.id,
        }) }}

        <button class="btn submit" type="submit">Generate report</button>
    </form>

    <hr>

    <h2>Report</h2>

    <p>Showing report for {{ type }} event entry: <strong><a href="{{ selectedEntry.entry.cpEditUrl }}">{{ selectedEntry.entry.title }}</a>{{ ' (' ~ (selectedEntry.displayDate ? selectedEntry.displayDate|date : 'unknown date') ~ ')' }}</strong>.</p>

    {% if selectedEntry.entry.formie is empty %}
        <div class="readable">
            <blockquote class="note warning">This particular {{ type }} event entry does not have a valid Formie form associated with it!<br><br>If this is not expected, please adjust the {{ type }} entry <a href="{{ selectedEntry.entry.cpEditUrl }}">by clicking here</a>.</blockquote>
        </div>
    {% else %}

        {% set formieForm = selectedEntry.entry.formie.one() %}
        {% set hasNumberOfAttendees = formieForm.getFieldByHandle('numberOfAttendees') is not null %}

        {% set submissionCount = craft.formie.submissions()
            .formId(formieForm.id)
            .count()
        %}

        {# Orrrr do this with i18n (TODO) #}
        {% set plural = submissionCount != 1 ? 's' : '' %}

        <p>This {{ type }} entry uses the form <strong><a href="{{ formieForm.cpEditUrl }}">{{ formieForm.title }}</a></strong> which currently has {{ submissionCount }} submission{{ plural }}.</p>

        {% if submissionCount > 500 %}
            <p><strong>NOTE:</strong> Downloading an export for <em>all</em> submissions can take a while, particularly when there are 1000+ submissions. Please keep this tab open while you wait for the download to commence.</p>
        {% endif %}

        <div class="flex" style="margin-bottom: 1rem; align-items: center; gap: 10px;">
            <a class="btn js-download-all" data-formid="{{ formieForm.id }}" href="#">
                <span class="label">Download All {{ submissionCount}} Completed Submission{{ plural }}</span>
            </a>
            <div class="spinner hidden" id="export-spinner"></div>
        </div>

        {% if type == 'expo' %}
            {% set hasSeminarSessions = formieForm.getFieldByHandle('seminarSessions') is not null %}

            {% if not hasSeminarSessions %}
                <div class="readable">
                    <blockquote class="note warning">This particular expo entry has a Formie entry associated with it, but the form does not have any seminar sessions associated with it!<br><br>More specifically, the form does not have any field with the <strong>seminarSessions</strong> handle. Does the expo entry definitely have the correct form associated with it?</blockquote>
                </div>
            {% elseif not hasNumberOfAttendees %}
                <div class="readable">
                    <blockquote class="note warning">This particular expo entry has a Formie entry associated with it, but the form does not have a field for the number of attendees!<br><br>Does the expo entry definitely have the correct form associated with it?</blockquote>
                </div>
            {% else %}
                {# This is the happy path for expo! #}
                {% set sessionOptions = craft.registrationReporting.getExpoSessionSummary(formieForm) %}
            {% endif %}
        {% else %}
            {# This is the happy path for event! #}
            {% set sessionOptions = craft.registrationReporting.getEventSessionSummary(formieForm, selectedEntry.entry) %}
        {% endif %}

        {% if sessionOptions is defined %}
            {{ forms.editableTable({
                id: 'sessionTable',
                name: 'sessionTable',
                cols: {
                    session: {
                        type: 'heading',
                        heading: "Session Time",
                        thin: true
                    },
                    submissionTotal: {
                        type: 'number',
                        heading: "Number of submissions",
                        thin: true,
                    },
                    attendeesTotal: {
                        type: 'number',
                        heading: "Total number of attendees",
                        thin: true,
                    },
                    action: {
                        type: 'html',
                        heading: "Action",
                        thin: true,
                    },
                }|filter,
                rows: sessionOptions,
                fullWidth: false,
                allowAdd: false,
                allowDelete: false,
                allowReorder: false,
                errors: null,
                })
            }}
        {% endif %}

    {% endif %}
{% endif %}

{% js %}
$(document).ready(function() {
    // Handle the download all participants button click
    $('.js-download-all').on('click', function(e) {
        e.preventDefault();

        const $btn = $(this);
        const formId = $btn.data('formid');

        // Show loading spinner
        $('#export-spinner').removeClass('hidden');
        $btn.addClass('disabled');

        // Make the AJAX request
        $.ajax({
            url: '/admin/actions/registration-reporting/registration-reporting/get-all-participants',
            method: 'GET',
            data: { formId: formId, type: '{{ type }}' },
            success: function(response) {
                if (response.success) {
                    // Check immediately first
                    checkForFile(formId, function() {
                        // If not ready, start polling
                        pollForExport(formId);
                    });
                } else {
                    Craft.cp.displayError('Export failed to start');
                    $('#export-spinner').addClass('hidden');
                    $btn.removeClass('disabled');
                }
            },
            error: function(xhr) {
                Craft.cp.displayError('Export failed to start');
                $('#export-spinner').addClass('hidden');
                $btn.removeClass('disabled');
            }
        });
    });

    function checkForFile(formId, onNotReady, onSuccess) {
        $.ajax({
            url: '/admin/actions/registration-reporting/registration-reporting/download-export-job-file',
            method: 'GET',
            data: { formId: formId, type: '{{ type }}' },
            success: function(response) {
                if (response && typeof response === 'object' && !response.success) {
                    onNotReady();
                    return;
                }

                // File is ready, trigger download. Although this is kinda silly and we should have a different action
                // specifically for the download, and use this AJAX just for tracking the status, but eh... TODO
                window.location.href = '/admin/actions/registration-reporting/registration-reporting/download-export-job-file?formId=' + formId + '&type={{ type }}';
                $('#export-spinner').addClass('hidden');
                $('.js-download-all').removeClass('disabled');
                Craft.cp.displayNotice('Export complete!');

                if (onSuccess) {
                    onSuccess();
                }
            },
            error: function(xhr) {
                if (xhr.status !== 404 && xhr.status !== 200) {
                    $('#export-spinner').addClass('hidden');
                    $('.js-download-all').removeClass('disabled');
                    Craft.cp.displayError(xhr.responseText || 'Export failed');
                } else {
                    onNotReady();
                }
            }
        });
    }

    function pollForExport(formId) {
        let pollInterval = setInterval(function() {
            checkForFile(formId, function() {
                // Continue polling
            }, function() {
                // On success, stop polling
                clearInterval(pollInterval);
            });
        }, 5000);

        setTimeout(function() {
            clearInterval(pollInterval);
            $('#export-spinner').addClass('hidden');
            $('.js-download-all').removeClass('disabled');
            Craft.cp.displayError('Export timed out');
        }, 3 * 60 * 1000); // 3 minutes
    }
});
{% endjs %}

{% endblock %}

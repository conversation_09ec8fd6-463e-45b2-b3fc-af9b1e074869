<?php
namespace modules\registrationreporting\services;

use Craft;
use craft\base\Component;
use craft\elements\Entry;
use modules\attendeesreporting\services\FormSubmissionHelper;
use verbb\formie\elements\exporters\SubmissionExport;
use verbb\formie\elements\Form;
use verbb\formie\elements\Submission;
use yii\base\InvalidConfigException;
use yii\base\NotSupportedException;
use yii\db\Expression;

class RegistrationReportingService extends Component
{
    public function getExpoSessionSummary(Form $form): array {
        $sessions = $form->getFieldByHandle('seminarSessions');
        if (!$sessions) {
            return [];
        }

        $attendeesField = $form->getFieldByHandle('numberOfAttendees');
        if (!$attendeesField) {
            return [];
        }

        $sources = FormSubmissionHelper::getEntriesFromFirstValidSource($sessions->getInputSources());
        if (empty($sources)) {
            return [];
        }

        // Get all submissions with their session and attendee information using shared helper
        $results = FormSubmissionHelper::getFormSubmissionsWithSessionData(
            $form,
            $sessions->uid,
            $attendeesField->uid
        );

        $submissionTotal = [];
        $attendeesTotal = [];

        // Collect all unique session IDs first
        $allSessionIds = [];
        foreach ($results as $result) {
            $sessionIds = json_decode($result['session_ids'], true);
            if (!empty($sessionIds)) {
                $allSessionIds = array_merge($allSessionIds, $sessionIds);
            }
        }
        $allSessionIds = array_unique($allSessionIds);

        // Get all entries in one query
        $entries = Entry::find()
            ->id($allSessionIds)
            ->indexBy('id')
            ->all();

        // Now process the submissions using our cached entries
        foreach ($results as $result) {
            $sessionIds = json_decode($result['session_ids'], true);
            $numberOfAttendees = (int)json_decode($result['attendees']);

            if (empty($sessionIds) || empty($numberOfAttendees)) {
                continue;
            }

            foreach ($sessionIds as $sessionId) {
                if (!isset($entries[$sessionId])) {
                    continue;
                }

                $entry = $entries[$sessionId];
                if (!isset($attendeesTotal[$entry->uid])) {
                    $submissionTotal[$entry->uid] = 0;
                    $attendeesTotal[$entry->uid] = 0;
                }

                $submissionTotal[$entry->uid] += 1;
                $attendeesTotal[$entry->uid] += $numberOfAttendees;
            }
        }

        $summary = [];

        foreach($sources as $source) {
            $submissionTotalSource = isset($submissionTotal[$source->uid]) ? $submissionTotal[$source->uid] : 0;
            $attendeesTotalSource = isset($attendeesTotal[$source->uid]) ? $attendeesTotal[$source->uid] : 0;

            $isDisabled = $submissionTotalSource === 0 ? ' disabled' : '';

            $summary[] = [
                'session' => $source->title,
                'submissionTotal' => $submissionTotalSource,
                'attendeesTotal' => $attendeesTotalSource,
                'action' => Craft::$app->getView()->renderString(
                    <<<HTML
                    <a href="/actions/registration-reporting/registration-reporting/get-session-participants?formId={{ formId }}&sessionEntryId={{ sessionEntryId }}&sessionFieldUid={{ sessionFieldUid }}&type={{ type }}" class="{{ btnClass }}">
                        Download Registrant List
                    </a>
                    HTML,
                    [
                        'formId' => $form->id,
                        'sessionEntryId' => $source->id,
                        'sessionFieldUid' => $sessions->uid,
                        'type' => 'expo',
                        'btnClass' => "btn{$isDisabled}",
                    ]
                ),
            ];
        }

        return $summary;
    }

    public function getEventSessionSummary(Form $form, Entry $entry): array {
        $sessionTimes = $entry->eventSessionTimes->all();
        $summary = [];

        $sessionField = $form->getFieldByHandle('sessionSelect');
        if (!$sessionField) {
            return [];
        }

        $attendeesField = $form->getFieldByHandle('numberOfAttendees');
        if (!$attendeesField) {
            return [];
        }

        foreach ($sessionTimes as $session) {
            $date = $session->date->format('D j M Y');
            $time = $session->time->format('g:i a');
            $location = $session->branchSolo->one() ? $session->branchSolo->one()->title . ' Branch' : $session->locationTitle;

            // Get submissions for this session using shared helper
            $results = FormSubmissionHelper::getEventSessionSubmissions(
                $form,
                $session->id,
                $sessionField->uid,
                $attendeesField->uid
            );

            $submissionTotal = $results ? count($results) : 0;
            $attendees = array_map(fn($a) => (int) json_decode($a), array_column($results, 'attendees'));
            $attendeesTotal = array_sum($attendees);

            $isDisabled = $submissionTotal === 0 ? ' disabled' : '';

            $summary[] = [
                'session' => "$date at $time, $location",
                'submissionTotal' => $submissionTotal,
                'attendeesTotal' => $attendeesTotal,
                'action' => Craft::$app->getView()->renderString(
                        <<<HTML
                        <a href="/actions/registration-reporting/registration-reporting/get-session-participants?formId={{ formId }}&sessionEntryId={{ sessionEntryId }}&sessionFieldUid={{ sessionFieldUid }}&type={{ type }}" class="{{ btnClass }}">
                            Download Registrant List
                        </a>
                        HTML,
                        [
                            'formId' => $form->id,
                            'sessionEntryId' => $session->id,
                            'sessionFieldUid' => $sessionField->uid,
                            'type' => 'event',
                            'btnClass' => "btn{$isDisabled}",
                        ]
                    ),
            ];
        }

        return $summary;
    }

    /**
     * Create a query compatible with Formie's SubmissionExport class.
     * There are some minor differences between the query required for expos
     * vs. event registrations, so we have a type parameter to account for that.
     *
     * @param string $formId The ID of the form to get participants from
     * @param string $sessionEntryId The ID of the session entry
     * @param string $sessionFieldUid The UID of the session field
     * @param 'expo'|'event' $type The type of registration participant list we're fetching
     * @return array The list of participants
     *
     * @throws InvalidConfigException
     * @throws NotSupportedException
     */
    public function getSessionParticipantList(string $formId, string $sessionEntryId, string $sessionFieldUid, string $type): array {
        $db = Craft::$app->getDb();
        $serverVersion = $db->getServerVersion();
        $isMySQL = (stripos($serverVersion, 'mariadb') === false);

        // The event query expects the sessionEntryId to be a JSON value,
        // wrapped in quotes, so we treat that differently here.
        $sessionEntryIdQueryComponents = [ 'mysql' => '', 'mariadb' => ''];
        if ($type === 'expo') {
            $sessionEntryIdQueryComponents['mysql'] = "CAST(:sessionEntryId AS JSON),";
            $sessionEntryIdQueryComponents['mariadb'] = ":sessionEntryId,";
        } else if ($type === 'event') {
            $sessionEntryIdQueryComponents['mysql'] = "CAST(CONCAT('\"', :sessionEntryId, '\"') AS JSON),";
            $sessionEntryIdQueryComponents['mariadb'] = "CONCAT('\"', :sessionEntryId, '\"'),";
        } else {
            throw new InvalidConfigException("Invalid registration type: $type");
        }

        // Gross... I couldn't get a query that works for both MySQL and MariaDB,
        // so we have 2 expressions with slightly different syntax. A good sign
        // that we should have been using MySQL locally for this site from the start...
        $jsonExpression = $isMySQL ? "
        JSON_CONTAINS(
            JSON_EXTRACT(formie_submissions.content, CONCAT('$.\"', :sessionFieldUid, '\"')),
            {$sessionEntryIdQueryComponents['mysql']}
            '$'
        )" : "
        JSON_CONTAINS(
            JSON_EXTRACT(formie_submissions.content, CONCAT('$.', :sessionFieldUid)),
            {$sessionEntryIdQueryComponents['mariadb']}
            '$'
        )";

        $submissionQuery = Submission::find()
        ->formId($formId)
        ->andWhere([
            'and',
            new Expression(
                $jsonExpression,
                [
                    ':sessionFieldUid' => $sessionFieldUid,
                    ':sessionEntryId' => $sessionEntryId,
                ]
            ),
        ]);

        $exporter = new SubmissionExport();
        $exportedData = $exporter->export($submissionQuery);

        return $exportedData;
    }

    public function getAllParticipantsList(string $formId): array {
        $submissionQuery = Submission::find()
            ->formId($formId);

        $exporter = new SubmissionExport();
        $exportedData = $exporter->export($submissionQuery);

        return $exportedData;
    }

    /**
     * Returns a CSV filename for a payment report.
     *
     * @param string $formId
     * @return string
     */
    public function getCsvFilename(string $formId): string
    {
        return 'export-' . $formId . '-' . date('Y-m-d-H-i-s') . '.csv';
    }

    /**
     * Exports Formie export data to string-formatted CSV. Optionally, return the file pointer.
     *
     * Formie's export function returns data in the following format:
     *
     * Example:
     * ```
     * $data = [
     *     [
     *         'key1' => 'value1',
     *         'key2' => 'value2',
     *         'key3' => 'value3',
     *     ],
     *     [
     *         'key1' => 'value4',
     *         'key2' => 'value5',
     *         'key3' => 'value6',
     *     ],
     * ];
     * ```
     * ...where each outer array item has the same keys, so we use this as our CSV headers ($data[0])
     *
     * @param array $data The Formie export data to convert.
     * @return string|resource The generated CSV string.
     */
    public function formieExportToCsv(array $data, bool $returnFilePointer = false): mixed {
        $csv = '';
        $filePointer = fopen('php://temp', 'r+');

        if (!empty($data)) {
            fputcsv($filePointer, array_keys($data[0]));
        }

        // Now write each row of the data to the CSV
        foreach ($data as $row) {
            fputcsv($filePointer, $row);
        }

        // Rewind the stream and get the contents
        rewind($filePointer);

        if ($returnFilePointer && $filePointer) {
            return $filePointer;
        }

        $csv = stream_get_contents($filePointer);
        fclose($filePointer);

        return $csv;
    }
}

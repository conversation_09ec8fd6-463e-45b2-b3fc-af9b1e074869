<?php
/**
 * @link      https://github.com/Karmabunny
 * @copyright Copyright (c) 2025 Karmabunny
 */

namespace modules\submissionreporting\console\controllers;

use Craft;
use craft\console\Controller;
use Exception;
use modules\submissionreporting\SubmissionReporting;
use verbb\formie\elements\Form;
use verbb\formie\elements\Submission;
use yii\db\Expression;

/**
 * Submission reporting console commands.
 *
 * e.g. `./craft submission-reporting/submission-reporting`
 *
 * @package modules\submissionreporting\console\controllers
 */
class SubmissionReportingController extends Controller
{
    /** @inheritdoc */
    public $defaultAction = 'send-scheduled-submission-reports';

    /**
     * Sends out Formie notifications for any forms that have submission reporting enabled.
     *
     * @param string $frequency The frequency of the submission report to send (daily|weekly).
     * @return void
     */
    public function actionSendScheduledSubmissionReports(string $frequency)
    {
        $this->log('Starting scheduled submission reports');

        if ($frequency !== 'daily' && $frequency !== 'weekly') {
            $this->log('Invalid frequency: ' . $frequency . ' (must be daily or weekly)');
            return;
        }

        $submissionReportingService = SubmissionReporting::getInstance()->submissionReportingService;
        $frequencyExpression = $submissionReportingService->getFrequencyExpression($frequency);

        $forms = Form::find()
            ->template('submissionReports')
            ->all();


        // Step 1: Find any forms that are enabled/within the date range for reporting
        foreach ($forms as $form) {
            /** @var Form $form */
            $isReportingEnabled = $form->formieSubmissionReportingEnabled ?? null;

            if (!$isReportingEnabled) {
                continue;
            }

            $frequencySetting = $form->formieSubmissionReportingFrequency;

            if ($frequencySetting->value !== $frequency) {
                continue;
            }

            try {
                $recipientEmail = $form->formieSubmissionReportingEmail;

                $newSubmissionCount = $submissionReportingService->generateNewSubmissionCount(
                    $form->id,
                    $frequencyExpression,
                );

                if ($newSubmissionCount === 0) {
                    $this->log('No submissions found in last ' . $frequency . ' period: ' . $form->title);

                    // For now they want to send the report even if there are no new submissions.
                    // continue;
                }

                $submissionReportingService->sendCustomNotification($form, $recipientEmail);

                $this->log('Sent submission report successfully: ' . $form->title);
            } catch(Exception $e) {
                $this->log('Error processing form: ' . $form->title . ' - ' . $e->getMessage());
                // log trace
                $this->log('Trace: ' . $e->getTraceAsString());
            }
        }

        $this->log('Finished scheduled submission reports');
    }


    protected function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$timestamp}] {$message}\n";
        Craft::info("{$message}", 'send-scheduled-submission-reports');
    }
}

<?php

namespace modules\attendeesreporting\services;

use Craft;
use craft\elements\Entry;
use verbb\formie\elements\Form;
use yii\base\Component;

/**
 * Shared helper class for Formie reporting functionality
 */
class FormSubmissionHelper extends Component
{
    /**
     * Parses Formie input sources and returns the corresponding entries
     *
     * @param array $inputSources Array of Formie input source UIDs
     * @param string|null $orderBy Optional order by clause for the entries
     * @return Entry[] Array of entries from the parsed sources
     */
    public static function parseInputSourcesToEntries(array $inputSources, ?string $orderBy = 'cinemaLocation ASC, time ASC'): array
    {
        foreach ($inputSources as $sessionSourceUid) {
            // Via Formie, you can select either a section or entry type (or both) as a source for the field
            // and format will be, e.g. { [0]=> string(41) "type:UID" } or { [0]=> string(44) "section:UID" }
            [$prefix, $uid] = explode(':', $sessionSourceUid);

            switch ($prefix) {
                case 'type':
                    $entryType = Craft::$app->entries->getEntryTypeByUid($uid);
                    if (!$entryType) {
                        continue;
                    }

                    $query = Entry::find()->typeId($entryType->id);
                    if ($orderBy) {
                        $query->orderBy($orderBy);
                    }
                    $sources = $query->all();

                    if (!empty($sources)) {
                        return $sources;
                    }
                    break;

                case 'section':
                    $section = Craft::$app->entries->getSectionByUid($uid);
                    if (!$section) {
                        continue;
                    }

                    $query = Entry::find()->sectionId($section->id);
                    if ($orderBy) {
                        $query->orderBy($orderBy);
                    }
                    $sources = $query->all();

                    if (!empty($sources)) {
                        return $sources;
                    }
                    break;

                default:
                    continue;
            }
        }

        return [];
    }

    /**
     * Gets entries from the first valid Formie input source
     *
     * @param array $inputSources Array of Formie input source UIDs
     * @param string|null $orderBy Optional order by clause for the entries
     * @return Entry[] Array of entries from the first valid source
     */
    public static function getEntriesFromFirstValidSource(array $inputSources, ?string $orderBy = 'cinemaLocation ASC, time ASC'): array
    {
        return self::parseInputSourcesToEntries($inputSources, $orderBy);
    }

    /**
     * Gets form submissions with session and attendee data
     *
     * @param Form $form The form to query
     * @param string $sessionsFieldUid UID of the sessions field
     * @param string $attendeesFieldUid UID of the attendees field
     * @param array|null $dateRange Optional date range with 'start' and 'end' DateTime objects
     * @return array Array of submission data
     */
    public static function getFormSubmissionsWithSessionData(
        Form $form,
        string $sessionsFieldUid,
        string $attendeesFieldUid,
        ?array $dateRange = null
    ): array {
        $db = Craft::$app->getDb();

        $dateFilter = '';
        $params = [':formId' => $form->id];

        if ($dateRange && isset($dateRange['start']) && isset($dateRange['end'])) {
            $dateFilter = 'AND elements.dateCreated BETWEEN :startDate AND :endDate';
            $params[':startDate'] = $dateRange['start']->format('Y-m-d H:i:s');
            $params[':endDate'] = $dateRange['end']->format('Y-m-d H:i:s');
        }

        $results = $db->createCommand("
            SELECT
                elements.dateCreated,
                JSON_EXTRACT(formie_submissions.content, '$.\"" . $sessionsFieldUid . "\"') AS session_ids,
                JSON_EXTRACT(formie_submissions.content, '$.\"" . $attendeesFieldUid . "\"') AS attendees
            FROM
                (
                SELECT
                    elements.id AS elementsId,
                    elements_sites.id AS siteSettingsId
                FROM
                    {{%elements}} elements
                INNER JOIN {{%formie_submissions}} formie_submissions ON
                    formie_submissions.id = elements.id
                INNER JOIN {{%elements_sites}} elements_sites ON
                    elements_sites.elementId = elements.id
                WHERE
                    formie_submissions.formId = :formId
                    AND formie_submissions.isIncomplete = FALSE
                    AND formie_submissions.isSpam = FALSE
                    AND elements.enabled = TRUE
                    AND elements_sites.enabled = TRUE
                    AND elements.archived = FALSE
                    AND elements.dateDeleted IS NULL
                    AND elements.draftId IS NULL
                    AND elements.revisionId IS NULL
                    $dateFilter
                ORDER BY
                    elements.dateCreated DESC
                ) subquery
            INNER JOIN {{%elements}} elements ON
                elements.id = subquery.elementsId
            INNER JOIN {{%elements_sites}} elements_sites ON
                elements_sites.id = subquery.siteSettingsId
            INNER JOIN {{%formie_submissions}} formie_submissions ON
                formie_submissions.id = subquery.elementsId
            ORDER BY
                elements.dateCreated DESC
        ", $params)->queryAll();

        return $results;
    }

    /**
     * Gets form submissions for a specific event session
     *
     * @param Form $form The form to query
     * @param int $sessionId The ID of the specific session
     * @param string $sessionFieldUid UID of the session field
     * @param string $attendeesFieldUid UID of the attendees field
     * @param array|null $dateRange Optional date range with 'start' and 'end' DateTime objects
     * @return array Array of submission data
     */
    public static function getEventSessionSubmissions(
        Form $form,
        int $sessionId,
        string $sessionFieldUid,
        string $attendeesFieldUid,
        ?array $dateRange = null
    ): array {
        $db = Craft::$app->getDb();

        $dateFilter = '';
        $params = [':formId' => $form->id];

        if ($dateRange && isset($dateRange['start']) && isset($dateRange['end'])) {
            $dateFilter = 'AND elements.dateCreated BETWEEN :startDate AND :endDate';
            $params[':startDate'] = $dateRange['start']->format('Y-m-d H:i:s');
            $params[':endDate'] = $dateRange['end']->format('Y-m-d H:i:s');
        }

        $results = $db->createCommand("
            SELECT
                elements.dateCreated,
                JSON_EXTRACT(formie_submissions.content, '$.\"" . $attendeesFieldUid . "\"') AS attendees
            FROM
                (
                SELECT
                    elements.id AS elementsId,
                    elements_sites.id AS siteSettingsId
                FROM
                    {{%elements}} elements
                INNER JOIN {{%formie_submissions}} formie_submissions ON
                    formie_submissions.id = elements.id
                INNER JOIN {{%elements_sites}} elements_sites ON
                    elements_sites.elementId = elements.id
                WHERE
                    formie_submissions.formId = :formId
                    AND formie_submissions.isIncomplete = FALSE
                    AND formie_submissions.isSpam = FALSE
                    AND elements.enabled = TRUE
                    AND elements_sites.enabled = TRUE
                    AND elements.archived = FALSE
                    AND elements.dateDeleted IS NULL
                    AND elements.draftId IS NULL
                    AND elements.revisionId IS NULL
                    $dateFilter
                ORDER BY
                    elements.dateCreated DESC
                ) subquery
            INNER JOIN {{%elements}} elements ON
                elements.id = subquery.elementsId
            INNER JOIN {{%elements_sites}} elements_sites ON
                elements_sites.id = subquery.siteSettingsId
            INNER JOIN {{%formie_submissions}} formie_submissions ON
                formie_submissions.id = subquery.elementsId
            WHERE
                JSON_CONTAINS(JSON_EXTRACT(formie_submissions.content, '$.\"" . $sessionFieldUid . "\"'), '\"" . $sessionId . "\"')
            ORDER BY
                elements.dateCreated DESC
        ", $params)->queryAll();

        return $results;
    }
}
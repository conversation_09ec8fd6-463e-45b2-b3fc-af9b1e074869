<?php

namespace modules\attendeesreporting\services;

use Craft;
use DateInterval;
use DateTime;
use DateTimeZone;
use modules\attendeeslimit\AttendeesLimit;
use verbb\formie\elements\Form;
use craft\elements\Entry;
use yii\base\Component;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\base\NotSupportedException;
use yii\db\Exception as DbException;
use modules\attendeesreporting\services\FormSubmissionHelper;

/**
 * Attendees Limit service
 */
class AttendeesReporting extends Component
{
    /**
     * Gets the *total* number of attendees for a form
     *
     * @param Form $form
     * @return int
     * @throws Exception
     * @throws InvalidConfigException
     * @throws NotSupportedException
     * @throws DbException
     */
    public function getNumberOfAttendees(Form $form)
    {
        return AttendeesLimit::getInstance()->attendeesLimitService->getNumberOfAttendees($form);
    }

    /**
     * Gets the current reporting period dates (8am-8am)
     * @return array Array containing start and end dates in both local and UTC time
     */
    public function getReportingPeriod(): array
    {
        // Set timezone to Adelaide
        $iana_identifier = 'Australia/Adelaide';
        $timezone = new DateTimeZone($iana_identifier);
        $now = new DateTime('now', $timezone);
        $todayCutoff = new DateTime('today 8:00', $timezone);

        // If it's before 8am, we want two days ago 8am to yesterday 8am
        // If it's after 8am, we want yesterday 8am to today 8am
        if ($now < $todayCutoff) {
            $startDate = new DateTime('2 days ago 8:00', $timezone);
            $endDate = new DateTime('yesterday 8:00', $timezone);
        } else {
            $startDate = new DateTime('yesterday 8:00', $timezone);
            $endDate = clone $todayCutoff;
        }

        // Convert dates to UTC for database query
        $utcTimezone = new DateTimeZone('UTC');
        $startDateUtc = (clone $startDate)->setTimezone($utcTimezone);
        $endDateUtc = (clone $endDate)->setTimezone($utcTimezone);

        return [
            'local' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
            'utc' => [
                'start' => $startDateUtc,
                'end' => $endDateUtc,
            ],
            'iana' => $iana_identifier,
        ];
    }

    /**
     * Gets the number of attendees from submissions within the current reporting period (8am-8am)
     */
    public function getNewAttendeesCount(Form $form): int
    {
        $attendeesField = $form->getFieldByHandle('numberOfAttendees');
        if (!$attendeesField) {
            throw new Exception('Form does not have a field with the handle "numberOfAttendees"');
        }

        $period = $this->getReportingPeriod();
        $startDateUtc = $period['utc']['start']->format('Y-m-d H:i:s');
        $endDateUtc = $period['utc']['end']->format('Y-m-d H:i:s');

        $db = Craft::$app->getDb();
        $results = $db->createCommand("
            SELECT
                JSON_EXTRACT(formie_submissions.content, '$.\"" . $attendeesField->uid . "\"') AS attendees
            FROM
                (
                SELECT
                    elements.id AS elementsId,
                    elements_sites.id AS siteSettingsId
                FROM
                    {{%elements}} elements
                INNER JOIN {{%formie_submissions}} formie_submissions ON
                    formie_submissions.id = elements.id
                INNER JOIN {{%elements_sites}} elements_sites ON
                    elements_sites.elementId = elements.id
                WHERE
                    formie_submissions.formId = :formId
                    AND formie_submissions.isIncomplete = FALSE
                    AND formie_submissions.isSpam = FALSE
                    AND elements.enabled = TRUE
                    AND elements_sites.enabled = TRUE
                    AND elements.archived = FALSE
                    AND elements.dateDeleted IS NULL
                    AND elements.draftId IS NULL
                    AND elements.revisionId IS NULL
                    AND elements.dateCreated BETWEEN :startDate AND :endDate
                ORDER BY
                    elements.dateCreated DESC
                ) subquery
            INNER JOIN {{%elements}} elements ON
                elements.id = subquery.elementsId
            INNER JOIN {{%elements_sites}} elements_sites ON
                elements_sites.id = subquery.siteSettingsId
            INNER JOIN {{%formie_submissions}} formie_submissions ON
                formie_submissions.id = subquery.elementsId
            ORDER BY
                elements.dateCreated DESC
        ", [
            ':formId' => $form->id,
            ':startDate' => $startDateUtc,
            ':endDate' => $endDateUtc,
        ])->queryAll();

        $total = 0;
        foreach ($results as $result) {
            $total += (int)json_decode($result['attendees'], true);
        }

        return $total;
    }

    /**
     * Gets the time until the next reporting period update (next 8am)
     * @return DateInterval Time until next update
     */
    public function getTimeUntilNextUpdate(): DateInterval
    {
        $timezone = new DateTimeZone('Australia/Adelaide');
        $now = new DateTime('now', $timezone);
        $nextUpdate = new DateTime('today 8:00', $timezone);

        if ($now >= $nextUpdate) {
            $nextUpdate = new DateTime('tomorrow 8:00', $timezone);
        }

        return $now->diff($nextUpdate);
    }

    /**
     * Gets a summary of attendance for expo sessions
     *
     * @param Form $form The Formie form to get session data from
     * @return array Array of session data with new and total attendees
     */
    public function getExpoSessionAttendeeSummary(Form $form): array
    {
        $sessions = $form->getFieldByHandle('seminarSessions');
        if (!$sessions) {
            return [];
        }

        $attendeesField = $form->getFieldByHandle('numberOfAttendees');
        if (!$attendeesField) {
            return [];
        }

        $sources = FormSubmissionHelper::getEntriesFromFirstValidSource($sessions->getInputSources());
        if (empty($sources)) {
            return [];
        }

        $period = $this->getReportingPeriod();

        // Get all submissions with their session and attendee information using shared helper
        $allSubmissions = FormSubmissionHelper::getFormSubmissionsWithSessionData(
            $form,
            $sessions->uid,
            $attendeesField->uid
        );

        // Collect all unique session IDs first
        $allSessionIds = [];
        foreach ($allSubmissions as $submission) {
            $sessionIds = json_decode($submission['session_ids'], true);
            if (!empty($sessionIds)) {
                $allSessionIds = array_merge($allSessionIds, $sessionIds);
            }
        }
        $allSessionIds = array_unique($allSessionIds);

        // Get all entries in one query
        $entries = Entry::find()
            ->id($allSessionIds)
            ->indexBy('id')
            ->all();

        // Initialize counters
        $totalAttendeesPerSession = [];
        $newAttendeesPerSession = [];

        // Process submissions
        foreach ($allSubmissions as $submission) {
            $sessionIds = json_decode($submission['session_ids'], true);
            $attendeeCount = (int)json_decode($submission['attendees']);
            $submissionDate = new DateTime($submission['dateCreated']);

            if (empty($sessionIds) || empty($attendeeCount)) {
                continue;
            }

            $isNewSubmission = $submissionDate >= $period['utc']['start'] && $submissionDate <= $period['utc']['end'];

            foreach ($sessionIds as $sessionId) {
                if (!isset($entries[$sessionId])) {
                    continue;
                }

                $entry = $entries[$sessionId];

                // Initialize if not set
                if (!isset($totalAttendeesPerSession[$entry->uid])) {
                    $totalAttendeesPerSession[$entry->uid] = 0;
                    $newAttendeesPerSession[$entry->uid] = 0;
                }

                // Add to total count
                $totalAttendeesPerSession[$entry->uid] += $attendeeCount;

                // Add to new count if in reporting period
                if ($isNewSubmission) {
                    $newAttendeesPerSession[$entry->uid] += $attendeeCount;
                }
            }
        }

        // Build summary data
        $summary = [];
        foreach ($sources as $source) {
            $totalAttendees = $totalAttendeesPerSession[$source->uid] ?? 0;
            $newAttendees = $newAttendeesPerSession[$source->uid] ?? 0;

            $summary[] = [
                'session' => $source->title,
                'newAttendees' => $newAttendees,
                'totalAttendees' => $totalAttendees,
            ];
        }

        return $summary;
    }

    /**
     * Gets a summary of attendance for event sessions
     *
     * @param Form $form The Formie form to get session data from
     * @param Entry $entry The event entry
     * @return array Array of session data with new and total attendees
     */
    public function getEventSessionAttendeeSummary(Form $form, Entry $entry): array
    {
        $sessionTimes = $entry->eventSessionTimes->all();
        if (empty($sessionTimes)) {
            return [];
        }

        $sessionField = $form->getFieldByHandle('sessionSelect');
        if (!$sessionField) {
            return [];
        }

        $attendeesField = $form->getFieldByHandle('numberOfAttendees');
        if (!$attendeesField) {
            return [];
        }

        $period = $this->getReportingPeriod();
        $summary = [];

        foreach ($sessionTimes as $session) {
            $date = $session->date->format('D j M Y');
            $time = $session->time->format('g:i a');
            $location = $session->branchSolo->one() ? $session->branchSolo->one()->title . ' Branch' : $session->locationTitle;

            // Get submissions for this session using shared helper
            $results = FormSubmissionHelper::getEventSessionSubmissions(
                $form,
                $session->id,
                $sessionField->uid,
                $attendeesField->uid
            );

            // Calculate total and new attendees
            $totalAttendees = 0;
            $newAttendees = 0;

            foreach ($results as $result) {
                $attendeeCount = (int)json_decode($result['attendees']);
                $submissionDate = new DateTime($result['dateCreated']);

                $totalAttendees += $attendeeCount;

                // Check if submission is within reporting period
                if ($submissionDate >= $period['utc']['start'] && $submissionDate <= $period['utc']['end']) {
                    $newAttendees += $attendeeCount;
                }
            }

            $summary[] = [
                'session' => "$date at $time, $location",
                'newAttendees' => $newAttendees,
                'totalAttendees' => $totalAttendees,
            ];
        }

        return $summary;
    }
}

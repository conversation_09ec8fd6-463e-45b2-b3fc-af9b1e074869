<?php

namespace modules\urluploadassets\controllers;

use craft\web\Controller;
use craft\elements\Asset;
use craft\models\Volume;
use craft\fields\Assets as AssetField;
use Craft;
use craft\controllers\AssetsControllerTrait;
use craft\helpers\Assets;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Mime\MimeTypes;
use yii\base\UserException;
use yii\web\BadRequestHttpException;
use yii\web\Response;

class UploadController extends Controller
{
    use AssetsControllerTrait;

    protected array|int|bool $allowAnonymous = false;

    public function actionUpload(): Response
    {
        // Ensure the request is from the Control Panel
        if (!Craft::$app->getRequest()->getIsCpRequest()) {
            return $this->asJson(['success' => false, 'error' => 'Unauthorized']);
        }

        $this->requirePostRequest();
        $this->requireAdmin(false);

        // Get data from request
        $url = Craft::$app->getRequest()->getBodyParam('url');
        $fieldId = Craft::$app->getRequest()->getBodyParam('fieldId');
        $folderId = Craft::$app->getRequest()->getBodyParam('folderId');

        if (!$url) {
            return $this->asJson(['success' => false, 'error' => 'No URL provided']);
        }
        if (!$fieldId) {
            return $this->asJson(['success' => false, 'error' => 'Field ID missing.']);
        }

        // Get the field by ID
        $field = Craft::$app->fields->getFieldById($fieldId);
        if (!$field || !$field instanceof AssetField) {
            return $this->asJson(['success' => false, 'error' => 'Invalid asset field.']);
        }

        // Get the default upload volume from field settings
        $defaultVolumeUid = $field->defaultUploadLocationSource ? preg_replace('/^volume:/', '', $field->defaultUploadLocationSource) : null;
        $folderIdResolved = $field->resolveDynamicPathToFolderId();

        if (!$defaultVolumeUid) {
            return $this->asJson(['success' => false, 'error' => 'No default upload volume set for this field.']);
        }

        $volume = Craft::$app->volumes->getVolumeByUid($defaultVolumeUid);
        if (!$volume instanceof Volume) {
            return $this->asJson(['success' => false, 'error' => 'Invalid volume.']);
        }

        // If a folder ID is provided, validate it's within allowed sources
        if ($folderId) {
            $assets = Craft::$app->getAssets();
            $folder = $assets->findFolder(['id' => $folderId]);

            if (!$folder) {
                return $this->asJson(['success' => false, 'error' => 'Invalid folder ID.']);
            }

            try {
                $this->requireVolumePermissionByFolder('saveAssets', $folder);
            } catch (UserException $exception) {
                return $this->asJson(['success' => false, 'error' => $exception->getMessage()]);
            }
        } else {
            $folderId = $folderIdResolved;
        }

        $maxFileSize = Craft::$app->getConfig()->getGeneral()->maxUploadFileSize;

        // Fetch the file
        $client = HttpClient::create();
        $response = $client->request('GET', $url);

        // Inspect MIME
        $headers = $response->getHeaders();
        $mime = $headers['content-type'][0] ?? null;

        // Map MIME to extension
        $mimeTypes = new MimeTypes();
        $extensions = $mimeTypes->getExtensions($mime);

        if (empty($extensions)) {
            return $this->asJson(['success' => false, 'error' => "Unsupported MIME type: {$mime}"]);
        }

        $extension = $extensions[0];

        // Validate against allowed extensions in Craft
        $allowed = Craft::$app->getConfig()->getGeneral()->allowedFileExtensions;
        if (!in_array($extension, $allowed, true)) {
            return $this->asJson(['success' => false, 'error' => "File type not allowed: .{$extension}"]);
        }

        // Create Craft-approved filename
        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'] ?? '';
        $basename = pathinfo($path, PATHINFO_FILENAME) ?: 'downloaded-file';
        $filename = Assets::prepareAssetName("{$basename}.{$extension}");

        // Save to temp file
        $tempPath = Craft::$app->getPath()->getTempPath() . DIRECTORY_SEPARATOR . $filename;
        $handle = fopen($tempPath, 'wb');

        if (!$handle) {
            return $this->asJson(['success' => false, 'error' => 'Unable to open temp file for writing']);
        }

        // Stream and write to disk, with size check
        // avoids loading (potentially) huge files into memory
        $totalBytes = 0;
        foreach ($client->stream($response) as $chunk) {
            $data = $chunk->getContent();
            $totalBytes += strlen($data);

            if ($totalBytes > $maxFileSize) {
                fclose($handle);
                unlink($tempPath);
                return $this->asJson(['success' => false, 'error' => 'File exceeds maximum allowed size']);
            }

            fwrite($handle, $data);
        }

        fclose($handle);

        // Save the asset in the correct volume and folder
        // Check if an asset with this filename already exists in the folder
        $existingAsset = Asset::find()
            ->filename($filename)
            ->folderId($folderId)
            ->one();

        if ($existingAsset) {
            return $this->asJson([
                'success' => true,
                'assetId' => $existingAsset->id,
                'assetUrl' => $existingAsset->getUrl(),
            ]);
        }

        $asset = new Asset();
        $asset->tempFilePath = $tempPath;
        $asset->filename = $filename;
        $asset->newFolderId = $folderId;
        $asset->volumeId = $volume->id;

        if (!Craft::$app->elements->saveElement($asset)) {
            // Get all validation errors
            $errors = $asset->getErrors();
            $errorMessages = [];

            // Format each error into readable messages
            foreach ($errors as $attribute => $attributeErrors) {
                foreach ($attributeErrors as $error) {
                    $errorMessages[] = "$attribute: $error";
                }
            }

            return $this->asJson([
                'success' => false,
                'error' => 'Failed to save asset: ' . implode(', ', $errorMessages)
            ]);
        }

        return $this->asJson([
            'success' => true,
            'assetId' => $asset->id,
            'assetUrl' => $asset->getUrl(),
        ]);
    }

    /**
     * Get folder path information by folder ID.
     * Returns source path info for each folder up until the root folder.
     *
     * This feels like the type of thing that should be built into Craft,
     * but wasn't seeing anything exactly as I needed it in AssetsController.php,
     * and VolumeFolderSelectorModal.js specifically only returns the folder ID,
     * so I'm doing this for now.
     *
     * @return Response
     * @throws BadRequestHttpException
     * @throws InvalidConfigException
     */
    public function actionGetFolderPath(): Response
    {
        $this->requireAcceptsJson();
        $folderId = $this->request->getRequiredBodyParam('folderId');

        $assets = Craft::$app->getAssets();
        $folder = $assets->getFolderById($folderId);

        if (!$folder) {
            throw new BadRequestHttpException("Invalid folder ID: $folderId");
        }

        return $this->asJson([
            'sourcePath' => $folder->getSourcePathInfo(),
        ]);
    }
}

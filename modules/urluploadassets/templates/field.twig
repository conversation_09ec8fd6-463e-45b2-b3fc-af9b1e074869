{# URL Upload Assets Field Component #}
<div class="upload-by-url upload-by-url-{{ fieldId }}" style="display: none;"
    data-field-id="{{ fieldId }}"
    data-field-handle="{{ fieldHandle }}"
    data-upload-url="{{ actionUrl('url-upload-assets/upload/upload') }}"
    data-sources='{{ field.settings.sources|json_encode|e('html_attr') }}'
>
    <button type="button" class="btn dashed url-upload-trigger" data-icon="external" aria-label="Upload a file from a URL">Upload from URL</button>
</div>

<div class="modal-container small -js-url-upload-modal-{{ fieldId }}" style="display: none;">
    <div class="modal fitted">
        <div class="modal-header">
            <h2>Upload by URL</h2>
        </div>
        <div class="modal-content">
            <div class="field">
                <div class="heading">
                    <legend>
                        Enter a URL
                        <span class="visually-hidden">Required</span>
                        <span class="required" aria-hidden="true"></span>
                    </legend>
                </div>

                <input type="text" id="url-upload-input-{{ fieldId }}" class="text fullwidth url-upload-input" placeholder="https://example.com/image.jpg" required />
            </div>

            <div class="field">
                <div class="heading">
                    <legend>Upload Location (Optional)</legend>
                </div>
                <div class="input">
                    <div class="flex">
                        <div class="text">
                            <span class="selected-folder-text">Default location</span>
                        </div>
                        <button type="button" class="btn dashed folder-select-button" data-icon="folder">Choose folder</button>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <button type="button" class="btn submit url-upload-button">Upload</button>
                <button type="button" class="btn cancel">Cancel</button>
            </div>
        </div>
    </div>
</div>

{% js %}
// Insert the upload-by-url HTML into the .flex div
;(async () => {
    const uploadHtml = document.querySelector('.upload-by-url-{{ fieldId }}').outerHTML;
    const flexDiv = document.querySelector('#fields-{{ fieldHandle }} .flex');
    if (flexDiv) {
        flexDiv.insertAdjacentHTML('beforeend', uploadHtml);
        document.querySelector('.upload-by-url-{{ fieldId }}').style.display = 'block';
    }
})();
{% endjs %}

<?php

namespace modules\urluploadassets;

use Craft;
use craft\base\Event;
use craft\events\RegisterComponentTypesEvent;
use craft\events\RegisterTemplateRootsEvent;
use craft\services\Fields;
use craft\web\View;
use modules\urluploadassets\fields\UrlUploadAssetsField;
use modules\urluploadassets\resources\UrlUploadAssetsAsset;
use yii\base\Module as BaseModule;

/**
 * UrlUploadAssets module
 *
 * @method static UrlUploadAssets getInstance()
 */
class UrlUploadAssets extends BaseModule
{
    public function init(): void
    {
        Craft::setAlias('@modules/urluploadassets', __DIR__);

        // Set the controllerNamespace based on whether this is a console or web request
        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'modules\\urluploadassets\\console\\controllers';
        } else {
            $this->controllerNamespace = 'modules\\urluploadassets\\controllers';
        }

        parent::init();

        $this->attachEventHandlers();

        // Any code that creates an element query or loads Twig should be deferred until
        // after Craft is fully initialized, to avoid conflicts with other plugins/modules
        Craft::$app->onInit(function() {
            // ...
        });
    }

    private function attachEventHandlers(): void
    {
        // Register event handlers here ...
        // (see https://craftcms.com/docs/5.x/extend/events.html to get started)
        Event::on(
            Fields::class,
            Fields::EVENT_REGISTER_FIELD_TYPES,
            function (RegisterComponentTypesEvent $event) {
                $event->types[] = UrlUploadAssetsField::class;
            }
        );

        Event::on(
            View::class,
            View::EVENT_REGISTER_CP_TEMPLATE_ROOTS,
            function(RegisterTemplateRootsEvent $event) {
                $event->roots['url-upload-assets'] = __DIR__ . '/templates';
            }
        );

        Event::on(
            View::class,
            View::EVENT_BEFORE_RENDER_TEMPLATE,
            function() {
                // Only register in the control panel
                if (Craft::$app->getRequest()->getIsCpRequest()) {
                    Craft::$app->getView()->registerAssetBundle(UrlUploadAssetsAsset::class);
                }
            }
        );
    }
}

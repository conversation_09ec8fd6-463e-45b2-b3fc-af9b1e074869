// Initialize URL upload functionality
const initUrlUpload = () => {
    // Handle trigger button clicks
    $(document).on('click', '.url-upload-trigger', function(e) {
        e.preventDefault();
        const $trigger = $(this);
        const $container = $trigger.closest('.upload-by-url');
        const fieldId = $container.data('field-id');

        // Get the modal from the template
        const $modalContainer = $(`.-js-url-upload-modal-${fieldId}`);
        if (!$modalContainer.length) {
            console.error('Upload modal not found');
            return;
        }

        // Create modal instance
        const modal = new Garnish.Modal($modalContainer, {
            autoShow: false,
            onShow: () => {
                // Initialize UI elements when modal is shown
                Craft.initUiElements($modalContainer);
            }
        });

        // Initialize upload functionality for this modal instance
        const $modalContent = $modalContainer.find('.modal-content');
        const $urlUploadBtn = $modalContent.find('.url-upload-button');
        const $urlInput = $modalContent.find('.url-upload-input');
        const $folderSelectBtn = $modalContent.find('.folder-select-button');
        const $selectedFolderText = $modalContent.find('.selected-folder-text');
        const fieldHandle = $container.data('field-handle');
        const uploadUrl = $container.data('upload-url');
        const sources = $container.data('sources');

        // Handle folder selection
        $folderSelectBtn.on('click', function() {
            new Craft.VolumeFolderSelectorModal({
                sources,
                onSelect: ([targetFolder]) => {
                    if (targetFolder) {
                        $container.attr('data-selected-folder', `${targetFolder.folderId}`);

                        // add loading spinner next to select-folder button
                        const $loadingSpinner = $('<div class="spinner"></div>');
                        $folderSelectBtn.after($loadingSpinner);

                        Craft.sendActionRequest('POST', 'url-upload-assets/upload/get-folder-path', {
                            data: {
                                folderId: targetFolder.folderId
                            }
                        }).then(({data}) => {
                            $selectedFolderText.text(data.sourcePath.uri);
                            $loadingSpinner.remove();
                        });
                    }
                }
            });
        });

        // Handle URL upload
        $urlUploadBtn.on('click', async function() {
            const url = $urlInput.val();
            if (!url) {
                Craft.cp.displayError('Please enter a URL');
                return;
            }

            // Show loading state
            $urlUploadBtn.prop('disabled', true).addClass('disabled');

            try {
                const selectedFolder = $container.attr('data-selected-folder');
                const uploadResponse = await $.post(uploadUrl, {
                    url,
                    fieldId,
                    folderId: selectedFolder,
                    [Craft.csrfTokenName]: Craft.csrfTokenValue
                });

                if (!uploadResponse.success) {
                    Craft.cp.displayError(uploadResponse.error);
                    return;
                }

                const assetSelect = $(`#fields-${fieldHandle}`).data('elementSelect');
                if (!assetSelect) {
                    alert('Could not find asset field instance.');
                    return;
                }

                const assetId = uploadResponse.assetId;

                const renderResponse = await Craft.sendActionRequest('POST', 'app/render-elements', {
                    data: {
                        elements: [
                            {
                                type: 'craft\\elements\\Asset',
                                id: assetId,
                                siteId: assetSelect.settings.criteria.siteId,
                                instances: [
                                    {
                                        context: 'field',
                                        ui: ['list', 'large'].includes(assetSelect.settings.viewMode) ? 'chip' : 'card',
                                        size: assetSelect.settings.viewMode === 'large' ? 'large' : 'small',
                                    },
                                ],
                            },
                        ],
                    },
                });

                if (!renderResponse.data.elements || !renderResponse.data.elements[assetId]) {
                    Craft.cp.displayError('Failed to retrieve uploaded asset.');
                    return;
                }

                const elementInfo = Craft.getElementInfo(renderResponse.data.elements[assetId][0]);
                assetSelect.selectElements([elementInfo]);

                await Craft.appendHeadHtml(renderResponse.data.headHtml);
                await Craft.appendBodyHtml(renderResponse.data.bodyHtml);

                assetSelect.$container.trigger('change');
                Craft.cp.runQueue();
                Craft.cp.displaySuccess('File uploaded successfully!');
                modal.hide();
            } catch (error) {
                Craft.cp.displayError('An error occurred during upload.');
                console.error(error);
            } finally {
                $urlUploadBtn.prop('disabled', false).removeClass('disabled');
            }
        });

        // Handle cancel button
        $modalContent.find('.cancel').on('click', () => {
            modal.hide();
        });

        // Show the modal
        modal.show();
    });
};

// Initialize on page load
$(document).ready(function() {
    initUrlUpload();
});

// Check if we can add more elements and update UI accordingly
$(document).on("change", "[id^='fields-']", function() {
    const $field = $(this);

    // Try to get the asset select instance
    const assetSelect = $field.data("elementSelect");

    if (!assetSelect) {
        return;
    }

    // Find the entire upload-by-url container within this field's .flex div
    const $uploadContainer = $field.find(".flex .upload-by-url");
    if (!$uploadContainer.length) {
        return;
    }

    if (assetSelect.canAddMoreElements()) {
        $uploadContainer.removeClass("hidden");
    } else {
        $uploadContainer.addClass("hidden");
    }
});

<?php

namespace modules\urluploadassets\fields;

use craft\fields\Assets;
use Craft;

class UrlUploadAssetsField extends Assets
{
    public static function displayName(): string
    {
        return Craft::t('app', 'Assets with URL Upload');
    }

    public function getInputHtml($value, $element): string
    {
        $input = parent::getInputHtml($value, $element);

        $urlInput = Craft::$app->getView()->renderTemplate('url-upload-assets/field', [
            'field' => $this,
            'fieldId' => $this->id,
            'fieldHandle' => $this->handle,
        ]);

        return $input . $urlInput;
    }
}

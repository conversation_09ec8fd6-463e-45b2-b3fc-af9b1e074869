<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="icon" type="image/png" href="https://ckeditor.com/docs/ckeditor5/latest/assets/img/favicons/32x32.png" sizes="32x32">
    <meta charset="utf-8">
    <title>CKEditor 5 – Development Sample</title>
    <style>
        body {
            max-width: 800px;
            margin: 20px auto;
        }
    </style>
</head>
<body>

<h1>CKEditor 5 – Development Sample</h1>

<div id="editor">
    <h2>Development environment</h2>
    <p>
        This is a demo of the <a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/overview.html#classic-editor">classic editor
        build</a> that loads your plugin (<code>PhtDropdown</code>) generated by the tool. You can modify this
        sample and use it to validate whether a plugin or a set of plugins work fine.
    </p>
    <p>
        <code>PhtDropdown</code> inserts text into the editor. You can click the CKEditor 5 icon in the toolbar and see the results.
    </p>

    <h3>Helpful resources</h3>
    <ul>
        <li>Architecture
            <ul>
                <li>
                    <a href="https://ckeditor.com/docs/ckeditor5/latest/framework/guides/architecture/core-editor-architecture.html">Core editor architecture</a>
                </li>
            </ul>
            <ul>
                <li>
                    <a href="https://ckeditor.com/docs/ckeditor5/latest/framework/guides/architecture/editing-engine.html">The editing engine</a>
                </li>
            </ul>
            <ul>
                <li>
                    <a href="https://ckeditor.com/docs/ckeditor5/latest/framework/guides/architecture/ui-library.html">The UI library</a>
                </li>
            </ul>
        </li>

        <li>
            <a href="https://ckeditor.com/docs/ckeditor5/latest/framework/guides/support/browser-compatibility.html">Browser compatibility</a>
        </li>

        <li>
            <a href="https://ckeditor.com/docs/ckeditor5/latest/framework/guides/support/error-codes.html">The error codes</a>
        </li>

        <li><a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/development/dll-builds.html">The DLL builds</a></li>
    </ul>

    <h3>The directory structure</h3>

    <p>
        The code snippet below presents the directory structure.
    </p>

    <pre><code class="language-plaintext">.
├─ lang
│  └─ contexts.json    # Entries used for creating translations.
├─ sample
│  ├─ dll.html         # The editor initialized using the DLL builds. Check README for details.
│  ├─ index.html       # The currently displayed file.
│  └─ ckeditor.ts      # The editor initialization script.
├─ src
│  ├─ phtdropdown.ts
│  ├─ augmentation.ts  # Type augmentations for the `@ckeditor/ckeditor5-core` module. Read more in <a href="https://ckeditor.com/docs/ckeditor5/latest/api/module_core_plugincollection-PluginsMap.html">PluginsMap</a> and <a href="https://ckeditor.com/docs/ckeditor5/latest/api/module_core_commandcollection-CommandsMap.html">CommandsMap</a>.
│  ├─ index.ts         # The modules exported by the package when using the DLL builds.
│  └─ **/*.ts          # All TypeScript source files should be saved here.
├─ tests
│  ├─ phtdropdown.ts
│  ├─ index.ts         # Tests for the plugin.
│  └─ **/*.ts          # All tests should be saved here.
├─ theme
│  ├─ icons
│  │  ├─ ckeditor.svg  # The CKEditor 5 icon displayed in the toolbar.
│  │  └─ **/*.svg      # All icon files should be saved here.
│  └─ **/*.css         # All CSS files should be saved here.
├─ typings
│  └─ **/*.d.ts        # Files containing type definitions.
├─ .editorconfig
├─ ...
├─ README.md
└─ tsconfig.json       # TypeScript configuration file.</code></pre>

    <h3>Reporting issues</h3>
    <p>If you found a problem with CKEditor 5 or the package generator, please, report an issue:</p>
    <ul>
        <li><a href="https://github.com/ckeditor/ckeditor5/issues/new/choose">CKEditor 5</a></li>
        <li><a href="https://github.com/ckeditor/ckeditor5-package-generator/issues/new">The package generator</a></li>
    </ul>
</div>

<script src="./ckeditor.dist.js"></script>
</body>
</html>

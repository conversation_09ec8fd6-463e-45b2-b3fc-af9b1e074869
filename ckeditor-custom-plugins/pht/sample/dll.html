<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="icon" type="image/png" href="https://ckeditor.com/docs/ckeditor5/latest/assets/img/favicons/32x32.png" sizes="32x32">
    <meta charset="utf-8">
    <title>CKEditor 5 – DLL Sample</title>
    <style>
        body {
            max-width: 800px;
            margin: 20px auto;
        }
    </style>
</head>
<body>

<h1>CKEditor 5 – DLL Sample</h1>

<div id="editor">
    <h2>Production sample</h2>
    <p>
        This is a demo of the <a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/overview.html#classic-editor">classic editor
        build</a>, initialized using the <a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/development/dll-builds.html">DLL builds</a>.
    </p>
    <p>
        Your plugin (PhtDropdown) generated by the tool is already loaded into the editor. By default, it has an example button that adds some text to the editor. Whenever you change the plugin's name or toolbar items, make sure to update the editor configuration in the <code>sample/dll.html</code> file.
    </p>

    <h3>Uncaught TypeError</h3>
    <p>If the editor is not initialized correctly and the browser console displays an error such as the following:</p>
    <pre><code>Uncaught TypeError: Cannot read properties of undefined (reading 'PhtDropdown') at dll.html:85</code></pre>
    <p>it means that the DLL build of the <code>@karmabunny/ckeditor5-pht-dropdown</code> package has not been created.</p>
    <p>Please call the <code>npm run dll:build</code> command in the CLI, and after it finishes, refresh the page.</p>

    <h3>Anatomy of the DLL build</h3>
    <p>The source of the DLL build is located in the <code>src/index.ts</code> file. It may export as many things as the package offers.</p>

    <h4>Maintaining the sample</h4>
    <p>Whenever you change objects exported by the DLL build, please review the content of the sample. Things to keep in mind:</p>
    <ul>
        <li>Review the list of loaded plugins in the configuration.</li>
        <li>Review names of items registered in toolbars.</li>
    </ul>

    <h3>The goal</h3>
    <p>The primary purpose of the sample is to verify whether the plugins in the package will work together with CKEditor 5 created with the DLL approach.</p>

    <h3>Publishing the package</h3>
	<p>
		While releasing TypeScript package on npm, few things have to be taken care of:
	</p>
    <ul>
        <li>Building DLL</li>
        <li>Building TypeScript</li>
		<li>Changing the <code>main</code> filed in <code>package.json</code> to <code>.js</code> extension</li>
    </ul>
	<p>
		Likewise, after the release, there are few steps:
	</p>
	<ul>
        <li>Deleting compiled TypeScript files (they are generated in the <code>src</code> directory, and create needless clutter)</li>
		<li>Changing the <code>main</code> filed in <code>package.json</code> back to <code>.ts</code> extension</li>
    </ul>
    <p>
        When calling <code>npm publish</code>, those steps are handled automatically by predefined <code>prepublishOnly</code> and <code>postpublish</code> scripts. To learn more, see <a href="https://docs.npmjs.com/cli/v7/using-npm/scripts#pre--post-scripts">NPM docs</a>.
    </p>

    <h3>Reporting issues</h3>
    <p>If you found a problem with CKEditor 5 or the package generator, please, report an issue:</p>
    <ul>
        <li><a href="https://github.com/ckeditor/ckeditor5/issues/new/choose">CKEditor 5</a></li>
        <li><a href="https://github.com/ckeditor/create-ckeditor5-plugin/issues/new">The package generator</a></li>
    </ul>
</div>

<!-- DLL builds are served from the `node_modules/` directory -->
<script src="../node_modules/ckeditor5/build/ckeditor5-dll.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-editor-classic/build/editor-classic.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-code-block/build/code-block.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-essentials/build/essentials.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-basic-styles/build/basic-styles.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-heading/build/heading.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-autoformat/build/autoformat.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-block-quote/build/block-quote.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-image/build/image.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-link/build/link.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-indent/build/indent.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-media-embed/build/media-embed.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-list/build/list.js"></script>
<script src="../node_modules/@ckeditor/ckeditor5-table/build/table.js"></script>

<!-- The "@karmabunny/ckeditor5-pht-dropdown" package DLL build is served from the `build/` directory -->
<script src="../build/pht-dropdown.js"></script>

<script>
	console.log( 'Objects exported by the DLL build:', CKEditor5[ 'phtDropdown' ] );

	CKEditor5.editorClassic.ClassicEditor
		.create( document.querySelector( '#editor' ), {
			licenseKey: 'GPL',
			plugins: [
				CKEditor5[ 'phtDropdown' ].PhtDropdown,
				CKEditor5.essentials.Essentials,
				CKEditor5.autoformat.Autoformat,
				CKEditor5.blockQuote.BlockQuote,
				CKEditor5.basicStyles.Bold,
				CKEditor5.heading.Heading,
				CKEditor5.image.Image,
				CKEditor5.image.ImageCaption,
				CKEditor5.image.ImageStyle,
				CKEditor5.image.ImageToolbar,
				CKEditor5.image.ImageUpload,
				CKEditor5.indent.Indent,
				CKEditor5.basicStyles.Italic,
				CKEditor5.link.Link,
				CKEditor5.list.List,
				CKEditor5.mediaEmbed.MediaEmbed,
				CKEditor5.paragraph.Paragraph,
				CKEditor5.table.Table,
				CKEditor5.table.TableToolbar,
				CKEditor5.codeBlock.CodeBlock,
				CKEditor5.basicStyles.Code,
				CKEditor5.upload.Base64UploadAdapter
			],
			toolbar: [
				'phtDropdown',
				'|',
				'heading',
				'|',
				'bold',
				'italic',
				'link',
				'code',
				'bulletedList',
				'numberedList',
				'|',
				'outdent',
				'indent',
				'|',
				'uploadImage',
				'blockQuote',
				'insertTable',
				'mediaEmbed',
				'codeBlock',
				'|',
				'undo',
				'redo'
			],
			image: {
				toolbar: [
					'imageStyle:inline',
					'imageStyle:block',
					'imageStyle:side',
					'|',
					'imageTextAlternative'
				]
			},
			table: {
				contentToolbar: [
					'tableColumn',
					'tableRow',
					'mergeTableCells'
				]
			}
		} )
		.then( editor => {
			window.editor = editor;
		} )
		.catch( error => {
			console.error( 'There was a problem initializing the editor.', error );
		} );
</script>
</body>
</html>

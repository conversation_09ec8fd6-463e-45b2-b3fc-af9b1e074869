{
  "compilerOptions": {
    /**
     * TypeScript automagically loads typings from all "@types/*" packages if the "compilerOptions.types" array is not defined in
     * this file. However, if some dependencies have "@types/*" packages as their dependencies, they'll also be loaded as well.
     * As a result, TypeScript loaded "@types/node" which we don't want to use, because it allows using Node.js specific APIs that
     * are not available in the browsers.
     *
     * To avoid such issues, we defined this empty "types" to disable automatic inclusion of the "@types/*" packages.
     */
    "types": [],
    "lib": [
      "ES2019", // Must match the "target".
      "ES2020.String",
      "DOM",
      "DOM.Iterable"
    ],
    "noImplicitAny": true,
    "noImplicitOverride": true,
    "strict": true,
    "target": "es2019",
    "sourceMap": true,
    "allowJs": true,
	"moduleDetection": "force",
	"moduleResolution": "NodeNext",
	"module": "NodeNext",
	"skipLibCheck": true,
    "typeRoots": [
      "typings",
      "node_modules/@types"
    ]
  },
  "include": [
    "./sample",
    "./src",
    "./typings"
  ]
}

import { describe, expect, it } from 'vitest';
import { PhtDropdown as PhtDropdownDll, icons } from '../src/index.js';
import PhtDropdown from '../src/phtdropdown.js';

import ckeditor from './../theme/icons/ckeditor.svg';

describe( 'CKEditor5 PhtDropdown DLL', () => {
	it( 'exports PhtDropdown', () => {
		expect( PhtDropdownDll ).to.equal( PhtDropdown );
	} );

	describe( 'icons', () => {
		it( 'exports the "ckeditor" icon', () => {
			expect( icons.ckeditor ).to.equal( ckeditor );
		} );
	} );
} );

import { Plugin } from 'ckeditor5/src/core.js';
import {
	type ListDropdownItemDefinition,
	createDropdown,
	addListToDropdown
} from 'ckeditor5/src/ui.js';
import type { Writer } from 'ckeditor5/src/engine.js';
import { type Locale, Collection } from 'ckeditor5/src/utils.js';

import phtCkeditorIcon from '../theme/icons/pht-ckeditor-logo.svg';

export default class PhtDropdown extends Plugin {
	public static readonly pluginName = 'PhtDropdown' as const;

	public static get requires() {
		return [] as const;
	}

	public init(): void {
		const editor = this.editor;
		const t = editor.t;
		const model = editor.model;

		editor.ui.componentFactory.add( 'phtDropdown', ( locale: Locale ) => {
			// 1. Create the dropdown.
			const dropdownView = createDropdown( locale );

			// 2. Configure its button (the dropdown "trigger").
			dropdownView.buttonView.set( {
				label: t( 'PHT Richtext Tools' ),
				icon: phtCkeditorIcon,
				tooltip: true
			} );

			// 3. Create a list of items to populate the dropdown.
			const itemDefinitions = new Collection();
			itemDefinitions.add( {
				type: 'button',
				model: {
					label: t( 'Insert product table template' ),
					withText: true
				}
			} );

			// 4. Add the items to the dropdown.
			addListToDropdown(
				dropdownView,
				itemDefinitions as Collection<ListDropdownItemDefinition>
			);

			// 5. Now listen for the dropdown execute event and test again label (may need something more
			// robust if we end up adding more items in the future)
			dropdownView.on( 'execute', ( data: any ) => {
				const { label } = data.source;

				if ( label === t( 'Insert product table template' ) ) {
					model.change( ( writer: Writer ) => {
						// Create a table with one header row and one data row.
						const table = writer.createElement( 'table', { headingRows: 1 } );

						// Create the header row.
						const headerRow = writer.createElement( 'tableRow' );
						const headers = [
							'Depart',
							'Return',
							'Inside',
							'Outside',
							'Balcony'
						];
						headers.forEach( heading => {
							const headerCell = writer.createElement( 'tableCell' );
							const paragraph = writer.createElement( 'paragraph' );
							writer.insertText( heading, paragraph );
							writer.append( paragraph, headerCell );
							writer.append( headerCell, headerRow );
						} );

						writer.append( headerRow, table );

						// Create an empty data row.
						const dataRow = writer.createElement( 'tableRow' );
						for ( let i = 0; i < headers.length; i++ ) {
							const dataCell = writer.createElement( 'tableCell' );
							writer.appendElement( 'paragraph', dataCell );
							writer.append( dataCell, dataRow );
						}

						writer.append( dataRow, table );

						// Insert the table into the editor.
						model.insertContent( table );

						// Move the selection to the first cell of the data row.
						const firstCell = dataRow.getChild( 0 );
						if ( firstCell ) {
							writer.setSelection( firstCell, 'in' );
						}
					} );

					editor.editing.view.focus();
				}
			} );

			return dropdownView;
		} );
	}
}


## Cron scripts for Craft

These are pretty dumb but apparently robust enough.

Key things:

- Keeps cron related business in a tidy folder
- Naive logging into the Craft storage folder
- New tasks are registered directly within these scripts
- Logs are not readable by any (current) Craft tooling



### Install

1. Copy this folder into the repo (just not the README)
2. Update the sample `crontab` file (/docs/aws-codedeploy/cron/crontab):
3. Add your tasks to a sub script (weekly/daily/etc)
4. Commit all that
5. Code deploy will take care of installing it via /docs/aws-codedeploy/cron.sh



__Important notes:__

- Keep the 'weekly' script so logs are rotated
- The site must use a dotenv file (this should already be done)
- Console controllers are registered separately from web controllers
- Action paths are converted from CamelCase to kebab-case like so:
    - namespace: `modules\etc\MySerpModule\DataImportController::actionApiFetch()`
    - action: `my-serp/data-import/api-fetch`



### Future

Perhaps a cron plugin so we can register + log directly into Craft. Like, why not.


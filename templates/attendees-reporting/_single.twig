{% extends '01_core/main' %}

{% block meta %}
    {{ parent() }}
    <meta name="robots" content="noindex">
{% endblock %}

{% block top_bar %}
    <!-- Template: {{ _self }}.twig -->
    {{ parent() }}
{% endblock %}

{% block main_content %}
    {% embed "01_core/_layouts/page-layout.twig" %}

        {% block main_bar %}

            {% set entries = craft.entries()
                    .section('events')
                    .type(['expo', 'event'])
                    .status(null)
                    .all()
                %}
                {% set sortedEntries = entries|sort((a, b) => b.date <=> a.date) %}

                {% set period = craft.attendeesReporting.getReportingPeriod() %}
                {% set nextUpdate = craft.attendeesReporting.getTimeUntilNextUpdate() %}
                <p>
                    New attendees counted from <strong>{{ period.local.start|date('M j, Y g:ia') }}</strong> to <strong>{{ period.local.end|date('M j, Y g:ia') }}</strong> ({{ period.iana }})<br>
                    <small>Next reporting period update is in {{ nextUpdate.h }} {{ nextUpdate.h == 1 ? 'hour' : 'hours' }}, {{ nextUpdate.i }} {{ nextUpdate.i == 1 ? 'minute' : 'minutes' }}</small>
                </p>

                <figure class="table">
                    <table class="attendees-table">
                        <thead>
                            <tr>
                                <th scope="col" class="attendees-table__header attendees-table__header--form">Form / Session</th>
                                <th scope="col" class="attendees-table__header">New (24h)</th>
                                <th scope="col" class="attendees-table__header">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in sortedEntries %}
                                {% if entry.formie is not empty %}
                                    {% set formieForm = entry.formie.one() %}
                                    {% if formieForm and formieForm.getFieldByHandle('numberOfAttendees') %}
                                        <tr>
                                            <td><strong>{{ formieForm.title }}</strong></td>
                                            <td><strong>{{ craft.attendeesReporting.getNewAttendeesCount(formieForm) }}</strong></td>
                                            <td><strong>{{ craft.attendeesReporting.getNumberOfAttendees(formieForm) }}</strong></td>
                                        </tr>

                                        {# Get and display session data #}
                                        {% if entry.type == 'expo' %}
                                            {% set sessionSummary = craft.attendeesReporting.getExpoSessionAttendeeSummary(formieForm) %}
                                        {% else %}
                                            {% set sessionSummary = craft.attendeesReporting.getEventSessionAttendeeSummary(formieForm, entry) %}
                                        {% endif %}

                                        {% if sessionSummary|length > 0 %}
                                            {% for session in sessionSummary %}
                                                <tr>
                                                    <td class="attendees-table__row--indented">{{ session.session }}</td>
                                                    <td>{{ session.newAttendees }}</td>
                                                    <td>{{ session.totalAttendees }}</td>
                                                </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td class="attendees-table__row--indented"><em>No session data available</em></td>
                                                <td>-</td>
                                                <td>-</td>
                                            </tr>
                                        {% endif %}
                                    {% else %}
                                        <tr>
                                            <td><strong>{{ formieForm.title }}</strong></td>
                                            <td colspan="2"><em>No attendees field</em></td>
                                        </tr>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </figure>

                {% set entriesWithoutForms = sortedEntries|filter(entry => entry.formie is empty) %}
                {% if entriesWithoutForms|length > 0 %}
                    <div>
                        <p>
                            <strong>
                                {% if entriesWithoutForms|length == 1 %}
                                This entry does not have a form, so we can't provide an attendee summary for it:
                                {% else %}
                                These entries do not have forms, so we can't provide attendee summaries for them:
                                {% endif %}
                            </strong>
                        </p>
                        <ul>
                            {% for entry in entriesWithoutForms %}
                                <li>{{ entry.title }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                {# Feels silly to make Sass for a throwaway table like this - so for now I'll just stick with inlined CSS #}
                {% css %}
                    .attendees-table {
                        width: 100%;
                        table-layout: fixed;
                    }

                    .attendees-table__header {
                        width: 20%;
                    }

                    .attendees-table__header--form {
                        width: 60%;
                    }

                    .attendees-table__row--indented {
                        padding-left: 2rem;
                    }
                {% endcss %}

        {% endblock %}

    {% endembed %}


{% endblock %}

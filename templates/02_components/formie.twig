<!-- Template: {{ _self }}.twig -->

{% set hasMemoryStorage = hasMemoryStorage ?? false %}

{% if selectedForm is not defined %}
{% set selectedForm = block.formie ? block.formie.one() : null %}
{% endif %}

{# This is the suggested fix for when multi-page forms lose track of the fact they are on #}
{# the final page, which has happened to us a few times now... https://github.com/verbb/formie/issues/2160 #}
{% if hasMemoryStorage %}
{% do selectedForm.setStorageBehaviour('memory') %}
{% endif %}

{% set attendeesField = selectedForm.getFieldByHandle('numberOfAttendees') %}
{% set hasAttendeesLimitSettingOn = (craft.attendeesLimit.getAttendeesLimit(selectedForm) ?? false) and attendeesField is not null %}

{% if hasAttendeesLimitSettingOn %}
    {% set attendees = craft.attendeesLimit.getNumberOfAttendees(selectedForm) %}
    {% set hasHitAttendeesLimit = attendees >= selectedForm.formieAttendeesLimitNumber %}
    {% set attendeeProgress = attendees / selectedForm.formieAttendeesLimitNumber %}
{% endif %}

{% if hasHitAttendeesLimit ?? false %}
    <div class="fui-alert custom-formie-ribbon-alert">
        <p>We've reached the maximum number of attendees for this event. Registrations have now closed.</p>
        <p>Please contact <a href="mailto:<EMAIL>"><EMAIL></a> to express your interest in this event. </p>
    </div>
{% else %}
    {% if (attendeeProgress ?? 0) > 0.9 %}
        <div class="fui-alert custom-formie-ribbon-alert">
            <p>We're <strong>almost at capacity</strong> for this event. Please register soon to secure your place!</p>
        </div>
    {% endif %}
    {{ craft.formie.renderForm(selectedForm) }}
{% endif %}


{% set submitType = selectedForm.settings.submitMethod %}

{% set submitted = craft.formie.plugin.service.getFlash(selectedForm.id, 'submitted') %}

{% js %}

    {% if submitType == 'page-reload' %}

        {% if submitted %}
            window.dataLayer = window.dataLayer || [];

            dataLayer.push({
                event: 'completed_enquiry',
                formType: '{{ selectedForm.title }}',
            });
        {% endif %}

    {% else %}
    // ajax specific tracking js
            let $form = document.querySelector('#{{ selectedForm.getFormId() }}');

            if ($form) {
                $form.addEventListener('onAfterFormieSubmit', (e) => {
                e.preventDefault();

                    if (e.detail.nextPageId) {
                        return;
                    }

                    window.dataLayer = window.dataLayer || [];

                    dataLayer.push({
                        event: 'completed_enquiry',
                        formType: '{{ selectedForm.title }}',
                    });
                });
            }

    {% endif %}
{% endjs %}

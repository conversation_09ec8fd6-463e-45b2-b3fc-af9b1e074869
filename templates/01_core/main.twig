{#
    Set the page title
    Will be inherited by all templates
#}
{% if pageTitle is not defined and entry is defined %}
    {% set pageTitle = entry.title ?? null %}
{% elseif pageTitle is not defined and category is defined %}
    {% set pageTitle = category.title ?? null %}
{% elseif pageTitle is not defined and product is defined %}
    {% set pageTitle = product.title ?? null %}
{% elseif pageTitle is not defined %}
    {% set pageTitle = craft.app.request.segments|last|replace({ '-':' '})|title %}
{% endif %}

<!DOCTYPE html>
<html lang="{{ currentSite.language }}" id="html" class="no-js">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {% block meta %}
        {% include "01_core/_blocks/meta.twig" %}
    {% endblock %}

    {% block meta_page_title %}
        {% if seomatic is not defined or (seomatic is defined and not seomatic.meta.seoTitle) %}
            <title>
            {% if craft.app.config.general.devMode and craft.app.config.general.allowAdminChanges %}🚧{% endif %}
            {{ pageTitle }} | {{ siteName }}
            </title>
        {% endif %}
    {% endblock %}

    <base href="{{ craft.app.sites.currentSite.baseUrl }}">

    {# Favicon #}
    <link rel="apple-touch-icon" sizes="180x180" href="assets/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/favicon/favicon-16x16.png">
    <link rel="manifest" href="assets/favicon/site.webmanifest">
    <link rel="mask-icon" href="assets/favicon/safari-pinned-tab.svg" color="#fa7850">
    <link rel="shortcut icon" href="assets/favicon/favicon.ico">
    <meta name="msapplication-TileColor" content="#333333">
    <meta name="msapplication-config" content="assets/favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">

    {# javascript - external #}
    <script type="text/javascript">var ROOT = '/';</script>
    <script type="text/javascript" src="//ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>

    {# {% block css %}
        <link href="{{ busty('/css/base.css') }}" rel="stylesheet">
        <link href="{{ busty('/css/components.css') }}" rel="stylesheet"> #}
        {# <link href="{{ busty('/css/magnific-popup.css') }}" rel="stylesheet"> #}
        {# <link href="{{ busty('/css/print.css') }}" rel="stylesheet" media="print">
    {% endblock %} #}

{% block fonts %}
    <link rel="preload" href="https://use.typekit.net/lui2mgs.css" as="style">
    <link rel="stylesheet" href="https://use.typekit.net/lui2mgs.css">
{% endblock %}


    {# analytics #}
    {% include "01_core/_blocks/scripts-head.twig" %}

    {{ head() }}

    {# javascript - internal #}
    {% block js %}
    {{ craft.vite.script("vite/app.ts", false) }}
    {% js "/js/jquery.magnific-popup.min.js" %}
    {% endblock %}

</head>

<body {{ attr(bodyAttributes ?? {}) }}>
    {% include "01_core/_layouts/old-browser.twig" %}

    {% include "01_core/_blocks/scripts-body.twig" %}

    <a class="-vis-hidden" href="#content">Skip to Content</a>

    <div id="wrap" class="wrap">

        {% block ribbonAlert %}
            {% include "01_core/_layouts/ribbon-alert.twig" %}
        {% endblock %}


        {% block header %}
            {% include "01_core/_layouts/header.twig" %}
        {% endblock %}


        {% block top_bar %}{% endblock %}


        {% block banner %}
            {% include '02_components/bannerInner' %}
        {% endblock %}


        <section id="content">


        {% include '01_core/_layouts/site-alerts' %}


        {% block main_content %}
        {% endblock %}

        </section>


        {% block footer %}
            {% include "01_core/_layouts/footer.twig" %}
        {% endblock %}


    </div>


    {% block outside_wrap %}
    {% endblock %}

    {% block modalWindow %}
        {% include "01_core/_layouts/modal-window.twig" %}
    {% endblock %}

</body>

</html>

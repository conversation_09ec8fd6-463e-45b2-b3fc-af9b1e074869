{"handle": "airfaresEnquiry", "settings": "{\"displayFormTitle\":false,\"displayCurrentPageTitle\":false,\"displayPageTabs\":false,\"displayPageProgress\":false,\"scrollToTop\":true,\"progressPosition\":\"end\",\"defaultLabelPosition\":\"verbb\\\\formie\\\\positions\\\\AboveInput\",\"defaultInstructionsPosition\":\"verbb\\\\formie\\\\positions\\\\AboveInput\",\"requiredIndicator\":\"asterisk\",\"submitMethod\":\"ajax\",\"submitAction\":\"message\",\"submitActionTab\":null,\"submitActionUrl\":null,\"submitActionFormHide\":false,\"submitActionMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Submission saved.\\\"}]}]\",\"submitActionMessageTimeout\":null,\"submitActionMessagePosition\":\"top-form\",\"loadingIndicator\":\"spinner\",\"loadingIndicatorText\":null,\"validationOnSubmit\":true,\"validationOnFocus\":false,\"errorMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Couldn’t save submission due to errors.\\\"}]}]\",\"errorMessagePosition\":\"top-form\",\"requireUser\":false,\"requireUserMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"scheduleForm\":false,\"scheduleFormStart\":null,\"scheduleFormEnd\":null,\"scheduleFormPendingMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"scheduleFormExpiredMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"limitSubmissions\":false,\"limitSubmissionsNumber\":null,\"limitSubmissionsType\":\"total\",\"limitSubmissionsMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"integrations\":{\"elementTest\":{\"enabled\":\"\",\"optInField\":\"\",\"entryTypeSection\":\"\",\"defaultAuthorId\":[\"7\"],\"attributeMapping\":{\"title\":\"\",\"siteId\":\"\",\"slug\":\"\",\"author\":\"\",\"postDate\":\"\",\"expiryDate\":\"\",\"enabled\":\"\",\"dateCreated\":\"\",\"dateUpdated\":\"\"},\"fieldMapping\":\"\",\"overwriteValues\":\"\",\"createDraft\":\"\",\"updateElement\":\"\",\"updateElementMapping\":\"\",\"updateSearchIndexes\":\"1\"},\"recaptcha\":{\"enabled\":\"1\",\"showAllPages\":\"\"},\"mailchimp\":{\"enabled\":\"1\",\"optInField\":\"{field:subscribe}\",\"listId\":\"de51280a13\",\"fieldMapping\":{\"email_address\":\"{field:email}\",\"FNAME\":\"{field:name1.firstName}\",\"LNAME\":\"{field:name1.lastName}\"},\"appendTags\":\"\",\"useDoubleOptIn\":\"\"}},\"submissionTitleFormat\":\"{timestamp}\",\"collectIp\":false,\"collectUser\":false,\"dataRetention\":null,\"dataRetentionValue\":null,\"fileUploadsAction\":null,\"redirectUrl\":null,\"pageRedirectUrl\":null,\"defaultEmailTemplateId\":\"1\",\"disableCaptchas\":false}", "submitActionEntryId": null, "submitActionEntrySiteId": null, "defaultStatusId": 1, "dataRetention": "years", "dataRetentionValue": 1, "userDeletedAction": "retain", "fileUploadsAction": "retain", "title": "Airfares Enquiry", "notifications": [{"name": "Airfares Enquiry Form Submission", "handle": "eventFollowUpFormSubmission", "enabled": 1, "subject": "New {formName} Form Submission", "recipients": "email", "to": "<EMAIL>", "toConditions": null, "cc": null, "bcc": null, "replyTo": "{field:email}", "replyToName": "{field:name1.__toString}", "from": "{systemEmail}", "fromName": "{siteName}", "sender": null, "content": "[{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"text\":\"New \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Form Name\",\"value\":\"{formName}\"}},{\"type\":\"text\",\"text\":\" Submission\"}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"}},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"All Form Fields\",\"value\":\"{allFields}\"}}]}]", "attachFiles": 1, "attachPdf": null, "attachAssets": "[]", "enableConditions": 0, "conditions": null, "customSettings": "[]", "uid": "5c8a71a0-7594-4ee5-9d36-fdedbd8596f9"}], "pages": [{"label": "Page 1", "settings": {"submitButtonLabel": "Submit", "backButtonLabel": "Back", "showBackButton": false, "saveButtonLabel": "Save", "showSaveButton": false, "saveButtonStyle": "link", "buttonsPosition": "left", "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "enableNextButtonConditions": false, "nextButtonConditions": [], "enablePageConditions": false, "pageConditions": [], "enableJsEvents": false, "jsGtmEventOptions": []}, "rows": [{"fields": [{"type": "verbb\\formie\\fields\\Name", "settings": {"label": "Name", "handle": "name1", "instructions": null, "required": false, "useMultipleFields": true, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": "verbb\\formie\\positions\\AboveInput", "cssClasses": null, "containerAttributes": [], "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "contentTable": null, "subFieldLabelPosition": null, "rows": [{"fields": [{"type": "verbb\\formie\\fields\\subfields\\NamePrefix", "settings": {"label": "Prefix", "handle": "prefix", "instructions": null, "required": false, "optgroups": true, "limitOptions": false, "min": null, "max": null, "enabled": false, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "honorific-prefix"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "options": [{"label": "Select an option", "value": ""}, {"label": "Mr.", "value": "mr"}, {"label": "Mrs.", "value": "mrs"}, {"label": "Ms.", "value": "ms"}, {"label": "Miss.", "value": "miss"}, {"label": "Mx.", "value": "mx"}, {"label": "Dr.", "value": "dr"}, {"label": "Prof.", "value": "prof"}], "multi": false, "layout": null}}, {"type": "verbb\\formie\\fields\\subfields\\NameFirst", "settings": {"label": "First Name", "handle": "firstName", "instructions": null, "required": true, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "given-name"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}, {"type": "verbb\\formie\\fields\\subfields\\NameMiddle", "settings": {"label": "Middle Name", "handle": "middleName", "instructions": null, "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": false, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "additional-name"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}, {"type": "verbb\\formie\\fields\\subfields\\NameLast", "settings": {"label": "Last Name", "handle": "lastName", "instructions": null, "required": true, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "family-name"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}]}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Email", "settings": {"label": "Email", "handle": "email", "instructions": null, "required": true, "validateDomain": false, "blockedDomains": [], "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Phone", "settings": {"label": "Phone Number", "handle": "phoneNumber", "instructions": null, "required": false, "countryEnabled": false, "countryDefaultValue": null, "countryLanguage": "auto", "countryAllowed": [], "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Entries", "settings": {"label": "Preferred Branch", "handle": "preferredBranch", "instructions": null, "required": false, "enabled": true, "matchField": null, "placeholder": "Select an branch", "defaultValue": [], "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": {"showRule": "show", "conditionRule": "all", "conditions": []}, "enableContentEncryption": false, "visibility": null, "sources": ["section:ad2295ee-2dd4-4c3a-9478-3d2d4602c42a"], "source": null, "limitOptions": null, "displayType": "dropdown", "labelSource": "title", "orderBy": "title ASC", "multi": false}}]}, {"fields": [{"type": "verbb\\formie\\fields\\SingleLineText", "settings": {"label": "Do you have a preferred Consultant?", "handle": "doYouHaveAPreferredConsultant", "instructions": null, "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Hidden", "settings": {"label": "Airfare of interest", "handle": "airfareOfInterest", "instructions": null, "required": false, "defaultOption": "currentUrl", "queryParameter": null, "cookieName": null, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": "https://www.pht.com.au/admin/formie/settings/import-export", "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Agree", "settings": {"label": "Subscribe", "handle": "subscribe", "instructions": null, "required": false, "description": [{"type": "paragraph", "attrs": {"textAlign": "start"}, "content": [{"type": "text", "text": "I'd like to subscribe to the Phil Hoffmann Travel email newsletter"}]}], "checkedValue": "Yes", "uncheckedValue": "No", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": false, "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}]}], "exportVersion": "v3"}
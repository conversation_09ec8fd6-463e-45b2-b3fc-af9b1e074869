{"handle": "GiftRegistryContribution", "settings": "{\"displayFormTitle\":false,\"displayCurrentPageTitle\":false,\"displayPageTabs\":true,\"displayPageProgress\":false,\"scrollToTop\":true,\"progressPosition\":\"end\",\"defaultLabelPosition\":\"verbb\\\\formie\\\\positions\\\\AboveInput\",\"defaultInstructionsPosition\":\"verbb\\\\formie\\\\positions\\\\AboveInput\",\"requiredIndicator\":\"asterisk\",\"submitMethod\":\"ajax\",\"submitAction\":\"message\",\"submitActionTab\":null,\"submitActionUrl\":null,\"submitActionFormHide\":true,\"submitActionMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Payment received.\\\"},{\\\"type\\\":\\\"hardBreak\\\"},{\\\"type\\\":\\\"hardBreak\\\"},{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Thank you for your contribution to the \\\"},{\\\"type\\\":\\\"variableTag\\\",\\\"attrs\\\":{\\\"label\\\":\\\"Last names of gift recipients\\\",\\\"value\\\":\\\"{field:lastNameOfGiftRecipient}\\\"}},{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\" gift registry!\\\"}]}]\",\"submitActionMessageTimeout\":null,\"submitActionMessagePosition\":\"top-form\",\"loadingIndicator\":\"spinner\",\"loadingIndicatorText\":null,\"validationOnSubmit\":true,\"validationOnFocus\":false,\"errorMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Couldn’t save submission due to errors.\\\"}]}]\",\"errorMessagePosition\":\"top-form\",\"requireUser\":false,\"requireUserMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"scheduleForm\":false,\"scheduleFormStart\":null,\"scheduleFormEnd\":null,\"scheduleFormPendingMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"scheduleFormExpiredMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"limitSubmissions\":false,\"limitSubmissionsNumber\":null,\"limitSubmissionsType\":\"total\",\"limitSubmissionsMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"integrations\":{\"elementTest\":{\"enabled\":\"\",\"optInField\":\"\",\"entryTypeSection\":\"\",\"defaultAuthorId\":[\"7\"],\"attributeMapping\":{\"title\":\"\",\"siteId\":\"\",\"slug\":\"\",\"author\":\"\",\"postDate\":\"\",\"expiryDate\":\"\",\"enabled\":\"\",\"dateCreated\":\"\",\"dateUpdated\":\"\"},\"fieldMapping\":\"\",\"overwriteValues\":\"\",\"createDraft\":\"\",\"updateElement\":\"\",\"updateElementMapping\":\"\",\"updateSearchIndexes\":\"1\"},\"recaptcha\":{\"enabled\":\"1\",\"showAllPages\":\"\"},\"mailchimp\":{\"enabled\":\"1\",\"optInField\":\"{field:subscribe}\",\"listId\":\"de51280a13\",\"fieldMapping\":{\"email_address\":\"{field:email}\",\"FNAME\":\"{field:namesOfGiftContributors}\"},\"appendTags\":\"\",\"useDoubleOptIn\":\"\"}},\"submissionTitleFormat\":\"{timestamp}\",\"collectIp\":false,\"collectUser\":false,\"dataRetention\":null,\"dataRetentionValue\":null,\"fileUploadsAction\":null,\"redirectUrl\":null,\"pageRedirectUrl\":null,\"defaultEmailTemplateId\":\"1\",\"disableCaptchas\":false}", "submitActionEntryId": null, "submitActionEntrySiteId": null, "defaultStatusId": 1, "dataRetention": "forever", "dataRetentionValue": null, "userDeletedAction": "retain", "fileUploadsAction": "retain", "title": "Gift Registry Contribution", "notifications": [{"name": "'Payment received' alert sent to branch emails", "handle": "formSubmissionAlertToPhtStaff", "enabled": 1, "subject": "New {formName} payment received", "recipients": "conditions", "to": null, "toConditions": "{\"toRecipients\":[{\"id\":\"new4487-2183\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"831\"},{\"id\":\"new6245-4493\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7221\"},{\"id\":\"new7011-3630\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7223\"},{\"id\":\"new8881-2967\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7225\"},{\"id\":\"new4123-4804\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7227\"},{\"id\":\"new2168-347\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7229\"},{\"id\":\"new6350-3831\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7231\"},{\"id\":\"new9437-7950\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7233\"},{\"id\":\"new4979-2590\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7235\"},{\"id\":\"new9482-401\",\"email\":\"<EMAIL>\",\"field\":\"{field:branch}\",\"condition\":\"=\",\"value\":\"7237\"}]}", "cc": null, "bcc": null, "replyTo": "{field:email}", "replyToName": "{field:name1.__toString}", "from": "{systemEmail}", "fromName": "{siteName}", "sender": null, "content": "[{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"text\":\"New \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Form Name\",\"value\":\"{formName}\"}},{\"type\":\"text\",\"text\":\" Submission\"}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Card details\",\"value\":\"{field:payment}\"}}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"All Form Fields\",\"value\":\"{allFields}\"}}]}]", "attachFiles": 1, "attachPdf": null, "attachAssets": "[]", "enableConditions": 0, "conditions": null, "customSettings": "[]", "uid": "3af65b79-8f77-4b62-9af9-59b84d8a8b30", "emailTemplate": {"name": "<PERSON><PERSON><PERSON>", "handle": "default", "template": "01_core/_emails/formie.twig", "sortOrder": null, "uid": "8a1ce942-fa35-4adc-bf2b-806fe175dfa7"}}, {"name": "'Payment received' alert sent to Finance department", "handle": "formSubmissionAlertToPhtStaff1", "enabled": 1, "subject": "New {formName} payment received", "recipients": "email", "to": "<EMAIL>", "toConditions": null, "cc": null, "bcc": null, "replyTo": "{field:email}", "replyToName": "{field:name1.__toString}", "from": "{systemEmail}", "fromName": "{siteName}", "sender": null, "content": "[{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"text\":\"New \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Form Name\",\"value\":\"{formName}\"}},{\"type\":\"text\",\"text\":\" payment received\"},{\"type\":\"hardBreak\"}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Card details\",\"value\":\"{field:payment}\"}}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"All Form Fields\",\"value\":\"{allFields}\"}}]}]", "attachFiles": 1, "attachPdf": null, "attachAssets": "[]", "enableConditions": 0, "conditions": null, "customSettings": "[]", "uid": "a710e4a9-2972-4e80-8212-2dfde24f6502", "emailTemplate": {"name": "<PERSON><PERSON><PERSON>", "handle": "default", "template": "01_core/_emails/formie.twig", "sortOrder": null, "uid": "8a1ce942-fa35-4adc-bf2b-806fe175dfa7"}}, {"name": "Client Receipt", "handle": "clientReceipt", "enabled": 1, "subject": "Your Gift Registry Contribution receipt", "recipients": "email", "to": "{field:email}", "toConditions": null, "cc": null, "bcc": null, "replyTo": "{systemEmail}", "replyToName": "{systemReplyTo}", "from": "{systemEmail}", "fromName": "{systemReplyTo}", "sender": null, "content": "[{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"File Number: \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"File Number\",\"value\":\"{field:fileNumber}\"}}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Last names of gift recipients: \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Last names of gift recipients\",\"value\":\"{field:lastNameOfGiftRecipient}\"}}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Branch:\"},{\"type\":\"text\",\"text\":\" \"}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Branch\",\"value\":\"{field:branch}\"}}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"bold\"}],\"text\":\"Names of gift contributors:\"},{\"type\":\"text\",\"text\":\" \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Names of gift contributors\",\"value\":\"{field:namesOfGiftContributors}\"}}]}]", "attachFiles": 1, "attachPdf": null, "attachAssets": "[]", "enableConditions": 0, "conditions": null, "customSettings": "[]", "uid": "4550e084-3b40-431a-8883-35b9393c2030", "emailTemplate": {"name": "Payment Receipt", "handle": "paymentReceipt", "template": "01_core/_emails/formie-payment-receipt.twig", "sortOrder": 1, "uid": "a4edf67b-5ec6-4e48-bf6d-f74ea56bc381"}}], "pages": [{"label": "Recipients", "settings": {"submitButtonLabel": "Submit", "backButtonLabel": "Back", "showBackButton": true, "saveButtonLabel": "Save", "showSaveButton": false, "saveButtonStyle": "link", "buttonsPosition": "left", "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "enableNextButtonConditions": false, "nextButtonConditions": [], "enablePageConditions": false, "pageConditions": [], "enableJsEvents": false, "jsGtmEventOptions": []}, "rows": [{"fields": [{"type": "verbb\\formie\\fields\\Heading", "settings": {"label": "Contribution details", "handle": "yourDetails", "instructions": null, "required": false, "headingSize": "h3", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\SingleLineText", "settings": {"label": "Last names of gift recipients", "handle": "lastNameOfGiftRecipient", "instructions": null, "required": true, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\SingleLineText", "settings": {"label": "File Number", "handle": "fileNumber", "instructions": null, "required": true, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Entries", "settings": {"label": "Branch", "handle": "branch", "instructions": null, "required": true, "enabled": true, "matchField": null, "placeholder": "Select an entry", "defaultValue": [], "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "sources": ["type:8d6d87ef-ca0b-48a9-abe5-6410b42ca443"], "source": null, "limitOptions": null, "displayType": "dropdown", "labelSource": "title", "orderBy": "title ASC", "multi": false}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Number", "settings": {"label": "Amount to contribute", "handle": "amountToContribute", "instructions": null, "required": true, "limit": false, "min": null, "max": null, "decimals": 0, "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Agree", "settings": {"label": "Please keep the value of my contribution confidential", "handle": "confidential", "instructions": null, "required": false, "description": [{"type": "paragraph", "attrs": {"textAlign": "start"}, "content": [{"type": "text", "text": "Please keep the value of my contribution confidential"}]}], "checkedValue": "Yes", "uncheckedValue": "No", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": false, "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Section", "settings": {"label": "Section Label 6f189c23d9ef0fb07b009e0ca834fe11", "handle": "sectionHandlecb37ee183b1d550fa91192b9caed5d21", "instructions": null, "required": false, "borderStyle": "solid", "borderWidth": 1, "borderColor": "#cccccc", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Heading", "settings": {"label": "Your details", "handle": "yourDetails1", "instructions": null, "required": false, "headingSize": "h3", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\SingleLineText", "settings": {"label": "Names of gift contributors", "handle": "namesOfGiftContributors", "instructions": null, "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\MultiLineText", "settings": {"label": "Personal Message", "handle": "personalMessage", "instructions": "Optional", "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "useRichText": false, "richTextButtons": ["bold", "italic"], "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Heading", "settings": {"label": "Payment details", "handle": "paymentDetails", "instructions": null, "required": false, "headingSize": "h5", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [{"label": "style", "value": "padding-top:8px"}], "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Email", "settings": {"label": "Email", "handle": "email", "instructions": null, "required": true, "validateDomain": false, "blockedDomains": [], "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Payment", "settings": {"label": "Card details", "handle": "payment", "instructions": null, "required": true, "paymentIntegration": "mint", "paymentIntegrationType": "modules\\mintpayment\\components\\MintPayment", "providerSettings": {"mint": {"integration": "modules\\mintpayment\\components\\MintPayment", "type": "single", "amountType": "dynamic", "currencyType": "fixed", "paymentBranchDestination": [], "metadata": [], "currencyFixed": "AUD", "amountVariable": "{field:amountToContribute}", "ccSurcharge": true, "cc3DS": true}}, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "includeInEmail": false, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Agree", "settings": {"label": "Subscribe", "handle": "subscribe", "instructions": null, "required": false, "description": [{"type": "paragraph", "attrs": {"textAlign": "start"}, "content": [{"type": "text", "text": "I'd like to subscribe to the Phil Hoffmann Travel email newsletter"}]}], "checkedValue": "Yes", "uncheckedValue": "No", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": false, "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}]}], "exportVersion": "v3"}
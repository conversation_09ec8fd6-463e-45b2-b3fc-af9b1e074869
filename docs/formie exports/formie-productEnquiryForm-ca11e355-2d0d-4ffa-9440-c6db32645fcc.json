{"handle": "productEnquiryForm", "settings": "{\"displayFormTitle\":false,\"displayCurrentPageTitle\":false,\"displayPageTabs\":false,\"displayPageProgress\":false,\"scrollToTop\":true,\"progressPosition\":\"end\",\"defaultLabelPosition\":\"verbb\\\\formie\\\\positions\\\\AboveInput\",\"defaultInstructionsPosition\":\"verbb\\\\formie\\\\positions\\\\AboveInput\",\"requiredIndicator\":\"asterisk\",\"submitMethod\":\"ajax\",\"submitAction\":\"message\",\"submitActionTab\":null,\"submitActionUrl\":null,\"submitActionFormHide\":true,\"submitActionMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Thank you for contacting <PERSON>. One of our Travel and Cruise Consultants will respond to your email within two business days.\\\"}]},{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"In the meantime, if your query is urgent, please don't hesitate to contact your nearest \\\"},{\\\"type\\\":\\\"text\\\",\\\"marks\\\":[{\\\"type\\\":\\\"link\\\",\\\"attrs\\\":{\\\"href\\\":\\\"find-a-branch\\\",\\\"target\\\":\\\"_blank\\\",\\\"rel\\\":\\\"noopener noreferrer nofollow\\\",\\\"class\\\":null}}],\\\"text\\\":\\\"Phil Hoffmann Travel branch\\\"},{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\" or call us on \\\"},{\\\"type\\\":\\\"text\\\",\\\"marks\\\":[{\\\"type\\\":\\\"link\\\",\\\"attrs\\\":{\\\"href\\\":\\\"tel:+1300-748-748\\\",\\\"target\\\":\\\"_blank\\\",\\\"rel\\\":\\\"noopener noreferrer nofollow\\\",\\\"class\\\":null}}],\\\"text\\\":\\\"1300 748 748\\\"},{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\".\\\"}]}]\",\"submitActionMessageTimeout\":null,\"submitActionMessagePosition\":\"top-form\",\"loadingIndicator\":\"spinner\",\"loadingIndicatorText\":null,\"validationOnSubmit\":true,\"validationOnFocus\":false,\"errorMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"Couldn’t save submission due to errors.\\\"}]}]\",\"errorMessagePosition\":\"top-form\",\"requireUser\":false,\"requireUserMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"scheduleForm\":false,\"scheduleFormStart\":null,\"scheduleFormEnd\":null,\"scheduleFormPendingMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"scheduleFormExpiredMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"limitSubmissions\":false,\"limitSubmissionsNumber\":null,\"limitSubmissionsType\":\"total\",\"limitSubmissionsMessage\":\"[{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"textAlign\\\":\\\"start\\\"}}]\",\"integrations\":{\"recaptcha\":{\"enabled\":\"1\",\"showAllPages\":\"\"},\"mailchimp\":{\"enabled\":\"1\",\"optInField\":\"\",\"listId\":\"de51280a13\",\"fieldMapping\":{\"email_address\":\"{field:email}\",\"FNAME\":\"{field:name1.firstName}\",\"LNAME\":\"{field:name1.lastName}\"},\"appendTags\":\"\",\"useDoubleOptIn\":\"\"}},\"submissionTitleFormat\":\"{timestamp}\",\"collectIp\":false,\"collectUser\":false,\"dataRetention\":null,\"dataRetentionValue\":null,\"fileUploadsAction\":null,\"redirectUrl\":null,\"pageRedirectUrl\":null,\"defaultEmailTemplateId\":null,\"disableCaptchas\":false}", "submitActionEntryId": null, "submitActionEntrySiteId": null, "defaultStatusId": 1, "dataRetention": "years", "dataRetentionValue": 2, "userDeletedAction": "retain", "fileUploadsAction": "retain", "title": "Product Enquiry Form", "notifications": [{"name": "Form submission alert to PHT staff", "handle": "formSubmissionAlertToPhtStaff", "enabled": 1, "subject": "New {formName} submission", "recipients": "email", "to": "<EMAIL>", "toConditions": "{\"toRecipients\":[{\"id\":\"new4508-9279\",\"email\":\"\",\"field\":\"\",\"condition\":\"\",\"value\":\"\"}]}", "cc": null, "bcc": null, "replyTo": "{field:email}", "replyToName": "{field:name1.__toString}", "from": "{systemEmail}", "fromName": "{siteName}", "sender": null, "content": "[{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"text\",\"text\":\"New \"},{\"type\":\"variableTag\",\"attrs\":{\"label\":\"Form Name\",\"value\":\"{formName}\"}},{\"type\":\"text\",\"text\":\" Submission\"}]},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"}},{\"type\":\"paragraph\",\"attrs\":{\"textAlign\":\"start\"},\"content\":[{\"type\":\"variableTag\",\"attrs\":{\"label\":\"All Form Fields\",\"value\":\"{allFields}\"}}]}]", "attachFiles": 1, "attachPdf": null, "attachAssets": "[]", "enableConditions": 0, "conditions": null, "customSettings": "[]", "uid": "a2c7005c-00ab-4b6a-8b5e-82f3fcfe1452"}], "pages": [{"label": "Page 1", "settings": {"submitButtonLabel": "Submit", "backButtonLabel": "Back", "showBackButton": false, "saveButtonLabel": "Save", "showSaveButton": false, "saveButtonStyle": "link", "buttonsPosition": "left", "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "enableNextButtonConditions": false, "nextButtonConditions": [], "enablePageConditions": false, "pageConditions": [], "enableJsEvents": false, "jsGtmEventOptions": []}, "rows": [{"fields": [{"type": "verbb\\formie\\fields\\Hidden", "settings": {"label": "Product", "handle": "product1", "instructions": null, "required": false, "defaultOption": "currentUrl", "queryParameter": null, "cookieName": null, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": "https://www.pht.com.au/admin/formie/settings/import-export", "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Name", "settings": {"label": "Name", "handle": "name1", "instructions": null, "required": true, "useMultipleFields": true, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": "verbb\\formie\\positions\\AboveInput", "cssClasses": null, "containerAttributes": [], "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "contentTable": null, "subFieldLabelPosition": null, "rows": [{"fields": [{"type": "verbb\\formie\\fields\\subfields\\NamePrefix", "settings": {"label": "Prefix", "handle": "prefix", "instructions": null, "required": false, "optgroups": true, "limitOptions": false, "min": null, "max": null, "enabled": false, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "honorific-prefix"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "options": [{"label": "Select an option", "value": ""}, {"label": "Mr.", "value": "mr"}, {"label": "Mrs.", "value": "mrs"}, {"label": "Ms.", "value": "ms"}, {"label": "Miss.", "value": "miss"}, {"label": "Mx.", "value": "mx"}, {"label": "Dr.", "value": "dr"}, {"label": "Prof.", "value": "prof"}], "multi": false, "layout": null}}, {"type": "verbb\\formie\\fields\\subfields\\NameFirst", "settings": {"label": "First Name", "handle": "firstName", "instructions": null, "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "given-name"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}, {"type": "verbb\\formie\\fields\\subfields\\NameMiddle", "settings": {"label": "Middle Name", "handle": "middleName", "instructions": null, "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": false, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "additional-name"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}, {"type": "verbb\\formie\\fields\\subfields\\NameLast", "settings": {"label": "Surname", "handle": "lastName", "instructions": null, "required": true, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": [{"label": "autocomplete", "value": "family-name"}], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}]}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Email", "settings": {"label": "Email", "handle": "email", "instructions": null, "required": true, "validateDomain": false, "blockedDomains": [], "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}, {"type": "verbb\\formie\\fields\\Phone", "settings": {"label": "Phone Number", "handle": "phoneNumber", "instructions": null, "required": false, "countryEnabled": false, "countryDefaultValue": null, "countryLanguage": "auto", "countryAllowed": [], "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Entries", "settings": {"label": "Do you have a preferred branch?", "handle": "preferredBranch", "instructions": null, "required": false, "enabled": true, "matchField": null, "placeholder": "Select an branch", "defaultValue": [], "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "sources": ["section:ad2295ee-2dd4-4c3a-9478-3d2d4602c42a"], "source": null, "limitOptions": null, "displayType": "dropdown", "labelSource": "title", "orderBy": "title ASC", "multi": false}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Radio", "settings": {"label": "Are you an existing client to Phil Hoffmann Travel?", "handle": "existingClient", "instructions": null, "required": false, "layout": "vertical", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "options": [{"isDefault": false, "label": "Yes", "value": "Yes"}, {"isDefault": false, "label": "No", "value": "No"}], "multi": false}}]}, {"fields": [{"type": "verbb\\formie\\fields\\SingleLineText", "settings": {"label": "Do you have a preferred consultant?", "handle": "whatIsYourConsultantsName", "instructions": null, "required": false, "limit": false, "min": null, "minType": "characters", "max": null, "maxType": "characters", "uniqueValue": false, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": true, "conditions": {"conditionRule": "all", "conditions": [{"condition": "=", "field": "{field:existingClient}", "id": "new3785-8269", "value": "Yes"}], "showRule": "show"}, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Radio", "settings": {"label": " Would you like to receive our enews with last minute hot deals, invitations and travel information?", "handle": "subscribe", "instructions": null, "required": false, "layout": "vertical", "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": null, "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null, "options": [{"isDefault": false, "label": "Yes, please subscribe me", "value": "Yes, please subscribe me"}, {"isDefault": false, "label": "No, I don’t wish to receive this information", "value": "No, I don’t wish to receive this information"}], "multi": false}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Hidden", "settings": {"label": "Product", "handle": "product", "instructions": null, "required": false, "defaultOption": "currentUrl", "queryParameter": null, "cookieName": null, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": "https://www.pht.com.au/admin/formie/settings/import-export", "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": [], "inputAttributes": [], "includeInEmail": true, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}, {"fields": [{"type": "verbb\\formie\\fields\\Html", "settings": {"label": "Privacy agreement", "handle": "privacyAgreement", "instructions": null, "required": false, "htmlContent": "By proceeding I agree to my personal information being handled in accordance with <a href=\"/privacy-policy\" target=\"_blank\">Phil Hoffmann Travel Privacy Policy</a>.", "purifyContent": true, "enabled": true, "matchField": null, "placeholder": null, "defaultValue": null, "prePopulate": null, "errorMessage": null, "labelPosition": "verbb\\formie\\positions\\Hidden", "instructionsPosition": null, "cssClasses": null, "containerAttributes": null, "inputAttributes": null, "includeInEmail": false, "emailValue": null, "enableConditions": false, "conditions": null, "enableContentEncryption": false, "visibility": null}}]}]}], "exportVersion": "v3"}
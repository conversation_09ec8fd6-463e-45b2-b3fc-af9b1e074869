limit_req_zone $binary_remote_addr zone=baselimit:10m rate=5r/s;

# do not permit default server access.
server {
    listen       80 default_server;
    listen       443 ssl;
    server_name  _;
    add_header   content-type text/plain;
    return       200 "configuration error: $WEB_DOMAIN_NAMES\n";

    ssl_certificate     /etc/ssl/certs/ssl-cert-snakeoil.pem;
    ssl_certificate_key /etc/ssl/private/ssl-cert-snakeoil.key;
}

# qa no index
server {
    listen       80;
    server_name  pht2.bunnyqa.com;
    add_header   content-type text/plain;
    return 410 "gone";
}

server {
    listen       80;
    server_name  $WEB_DOMAIN_NAMES;
    root         /home/<USER>/www/web;
    index        index index.php index.html;

    if ($host != $WEB_DOMAIN_CANONICAL) {
        return 302 $scheme://$WEB_DOMAIN_CANONICAL$request_uri;
    }

    # Custom redirects
    include /home/<USER>/nginx/redirects.conf;

    # Sprout
    include /home/<USER>/nginx/app.conf;

    # PHP-FPM
    location ~ \.php$ {
        limit_req zone=baselimit burst=50 delay=20;
        include /etc/nginx/fastcgi.conf;
        fastcgi_pass unix:/run/php/php8.2-fpm.sock;
    }

    # Disable the nginx server banner
    server_tokens off;

    # Enable HSTS (2 years)
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1;mode=block";

    # Enable compression of resources
    gzip on;
    gzip_min_length 250;
    gzip_proxied off;
    gzip_types text/css application/javascript image/svg+xml;
}

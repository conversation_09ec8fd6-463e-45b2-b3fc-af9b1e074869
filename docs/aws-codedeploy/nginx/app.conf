
port_in_redirect off;
absolute_redirect off;

# 404 error handler
error_page 404 /index.php?$query_string;

# 301 Redirect URLs with trailing /'s as per https://webmasters.googleblog.com/2010/04/to-slash-or-not-to-slash.html
rewrite ^/(.*)/$ /$1 permanent;

# Change // -> / for all URLs, so it works for our php location block, too
merge_slashes off;
rewrite (.*)//+(.*) $1/$2 permanent;

# Root directory location handler
location / {
    try_files $uri/index.html $uri $uri/ /index.php?$query_string;
}

# Craft-specific location handlers to ensure AdminCP requests route through index.php
# If you change your `cpTrigger`, change it here as well
location ^~ /admin {
    try_files $uri $uri/ /index.php?$query_string;
}

location ^~ /cpresources {
    try_files $uri $uri/ /index.php?$query_string;
}

# Disable reading of Apache .htaccess files
location ~ /\.ht {
    deny all;
}

# Misc settings
sendfile off;
client_max_body_size 100m;

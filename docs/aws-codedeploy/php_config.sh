#!/usr/bin/env bash
set -e
source "$(dirname "$0")/_config_"

# Run some php to get the php version
PHP_VERS=$( php -r "echo PHP_MAJOR_VERSION, '.', PHP_MINOR_VERSION;" )
if [ -z "$PHP_VERS" ]; then echo "Cannot determine PHP version"; exit 1; fi

# Determine php-fpm pool file location
FPM_CONF="/etc/php/${PHP_VERS}/fpm/pool.d/www.conf"
if [ ! -f "$FPM_CONF" ]; then echo "File '$FPM_CONF' not found"; exit 1; fi

# Determine php ini file location
PHP_CONF="/etc/php/${PHP_VERS}/fpm/php.ini"
if [ ! -f "$PHP_CONF" ]; then echo "File '$PHP_CONF' not found"; exit 1; fi


# Alter the PHP-FPM config
sed -E -i "s/^user = www-data/user = webapp/" $FPM_CONF
sed -E -i "s/^group = www-data/group = webapp/" $FPM_CONF
sed -E -i "s/^pm.max_children = [0-9]+/pm.max_children = 50/" $FPM_CONF
sed -E -i "s/^pm.start_servers = [0-9]+/pm.start_servers = 20/" $FPM_CONF
sed -E -i "s/^pm.min_spare_servers = [0-9]+/pm.min_spare_servers = 20/" $FPM_CONF
sed -E -i "s/^pm.max_spare_servers = [0-9]+/pm.max_spare_servers = 40/" $FPM_CONF
sed -E -i "s/^.*pm.max_requests = [0-9]+/pm.max_requests = 2000/" $FPM_CONF
sed -E -i "s/^.*request_terminate_timeout = [0-9]+/request_terminate_timeout = 150/" $FPM_CONF

# Alter the PHP.INI config
sed -E -i "s/upload_max_filesize = [0-9A-Z]+/upload_max_filesize = 100M/" $PHP_CONF
sed -E -i "s/post_max_size = [0-9A-Z]+/post_max_size = 100M/" $PHP_CONF
sed -E -i "s/memory_limit = [0-9A-Z]+/memory_limit = 256M/" $PHP_CONF
sed -E -i "s/max_input_vars = [0-9A-Z]+/max_input_vars = 3000/" $PHP_CONF
sed -E -i "s/max_execution_time = [0-9A-Z]+/max_execution_time = 120/" $PHP_CONF

# Alter the core max connections
echo "net.core.somaxconn = 65536" > /etc/sysctl.d/socket.conf
sysctl -p

# Service restart failsafes
mkdir -p /etc/systemd/system/php${PHP_VERS}-fpm.service.d
cd /etc/systemd/system/php${PHP_VERS}-fpm.service.d
rm * || true
cp /home/<USER>/php/override.conf override.conf

systemctl daemon-reload

#!/usr/bin/env bash
set -e
source "$(dirname "$0")/_config_"

export DEBIAN_FRONTEND=noninteractive
apt-get -y update


# Nginx and php
apt-get -y install nginx \
    php-fpm \
    php-cli \
    php-mysql \
    php-gd \
    php-xml \
    php-json \
    php-curl \
    php-soap \
    php-redis \
    php-zip \
    php-mbstring \
    php-bcmath \
    php-intl \
    php-imagick \
    php-fileinfo \
    php-exif \
    imagemagick \
    poppler-utils \
    cron \
    zip \
    rsync \
    jq \
    mariadb-client \
    redis-tools \
    ca-certificates \
    curl \
    gnupg \
    ssl-cert \
    certbot \
    python3-certbot-nginx \
    htop \
    goaccess


NODE_MAJOR=18

if ! grep -q "node_$NODE_MAJOR.x" /etc/apt/sources.list.d/nodesource.list; then
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg
    echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | sudo tee /etc/apt/sources.list.d/nodesource.list
    sudo apt-get update
fi

sudo apt-get install -y nodejs=*nodesource1
sudo npm install -g npm@^9

if ! which composer; then
    TARGET="composer-setup.php"

    wget -O "$TARGET" https://getcomposer.org/installer
    sudo php "$TARGET" --install-dir=/usr/local/bin --filename=composer
    rm $TARGET
else
    composer self-update
fi

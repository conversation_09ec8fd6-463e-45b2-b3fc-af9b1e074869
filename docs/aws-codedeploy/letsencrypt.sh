#!/usr/bin/env bash
set -e
source "$(dirname "$0")/_config_"

WEB_DOMAIN_NAMES="$(echo $WEB_DOMAIN_NAMES | tr ' ' ',')"

certbot --nginx \
    --non-interactive \
    --agree-tos \
    --email "<EMAIL>" \
    --domains "$WEB_DOMAIN_NAMES"

# Enable HTTP2
sed -E -i 's/listen 443 ssl;/listen 443 ssl http2;/' /etc/nginx/sites-enabled/production.conf

# special hack for bunnyqa as a separate certificate.
# ignores renewal errors - if any.
certbot --nginx \
    --non-interactive \
    --agree-tos \
    --email "<EMAIL>" \
    --domains "pht2.bunnyqa.com" || true
